import { Compo<PERSON>, OnIni<PERSON>, On<PERSON><PERSON><PERSON>, HostListener, ElementRef, ViewChild, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { GridsterConfig, GridsterItem, GridsterModule, GridType, CompactType, DisplayGrid } from 'angular-gridster2';
import { DashboardLoaderService } from './dashboard-loader.service';
import { Observable, Subscription, of, Subject } from 'rxjs';
import { catchError, map, debounceTime, switchMap, tap, take } from 'rxjs/operators';
import { FloorplanTileComponent } from '../tiles/floorplan/floorplan-tile.component';
import { LoadingSpinnerComponent, EmptyStateComponent } from '@angular-monorepo/dashboard-tiles';
import { HamburgerMenuComponent, MenuConfig } from '../shared/components/hamburger-menu/hamburger-menu.component';
import {
  Dashboard,
  DashboardItem,
  DashboardSnapshot,
  FloorplanTile,
  isFloorplanTile,
  TileType
} from '@angular-monorepo/dashboard-tiles';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { HAConfigComponent } from '../shared/components/ha-config/ha-config.component';
import { HomeAssistantService } from '../services/home-assistant.service';
import { ThemeService } from '../services/theme.service';
import { AddTileDialogComponent } from '../shared/components/add-tile-dialog/add-tile-dialog.component';
import { DeviceStatusTileComponent } from '../tiles/device-status/device-status-tile.component';
import { EntityBrowserTileComponent } from '../tiles/entity-browser/entity-browser-tile.component';
import { DeviceBrowserTileComponent } from '../tiles/device-browser/device-browser-tile.component';
import { BaseTileComponent, SampleTileComponent } from '@angular-monorepo/dashboard-tiles';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { RouterModule } from '@angular/router';
import { HaExplorerModalComponent } from '../shared/components/ha-explorer-modal/ha-explorer-modal.component';
import { NotificationService } from '../services/notification.service';

@Component({
  selector: 'app-gridster-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    GridsterModule,
    FloorplanTileComponent,
    DeviceStatusTileComponent,
    EntityBrowserTileComponent,
    DeviceBrowserTileComponent,
    BaseTileComponent,
    SampleTileComponent,
    LoadingSpinnerComponent,
    EmptyStateComponent,
    HamburgerMenuComponent,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatToolbarModule,
    MatTooltipModule,
    MatSnackBarModule
  ],
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.scss'
})
export class DashboardComponent implements OnInit, OnDestroy {
  options: GridsterConfig = {
    gridType: GridType.Fit,
    compactType: CompactType.None,
    margin: 10,
    outerMargin: true,
    outerMarginTop: null,
    outerMarginRight: null,
    outerMarginBottom: null,
    outerMarginLeft: null,
    useTransformPositioning: true,
    mobileBreakpoint: 640,
    minCols: 12,
    maxCols: 12,
    minRows: 12,
    maxRows: 12,
    maxItemCols: 12,
    minItemCols: 1,
    maxItemRows: 12,
    minItemRows: 1,
    maxItemArea: 144,
    minItemArea: 1,
    defaultItemCols: 1,
    defaultItemRows: 1,
    fixedColWidth: 105,
    fixedRowHeight: 105,
    keepFixedHeightInMobile: true,
    keepFixedWidthInMobile: false,
    scrollSensitivity: 10,
    scrollSpeed: 20,
    enableEmptyCellClick: false,
    enableEmptyCellContextMenu: false,
    enableEmptyCellDrop: false,
    enableEmptyCellDrag: false,
    enableOccupiedCellDrop: false,
    emptyCellDragMaxCols: 50,
    emptyCellDragMaxRows: 50,
    ignoreMarginInRow: false,
    draggable: {
      enabled: true,
    },
    resizable: {
      enabled: true,
    },
    swap: false,
    pushItems: true,
    disablePushOnDrag: false,
    disablePushOnResize: false,
    pushDirections: { north: true, east: true, south: true, west: true },
    pushResizeItems: true,
    displayGrid: DisplayGrid.None,
    disableWindowResize: false,
    disableAutoPositionOnConflict: false,
    itemChangeCallback: this.itemChange.bind(this),
    itemResizeCallback: this.itemResize.bind(this),
    gridInit: this.onGridInit.bind(this)
  };
  
  dashboard: Dashboard | null = null;
  items: DashboardItem[] = [];
  isLoading = true;
  isSaving = false;
  error: string | null = null;
  snapshots: DashboardSnapshot[] = [];
  isEditingEnabled = true;
  
  // Autosave functionality
  private changesSubject = new Subject<void>();
  public autoSaveEnabled = true;
  
  // Menu configuration
  menuConfig: MenuConfig = {
    autoSaveEnabled: this.autoSaveEnabled,
    isDefault: false,
    showGrid: false,
    canUndo: false,
    isSaving: false,
    dashboardName: 'Dashboard',
    isEditingEnabled: this.isEditingEnabled,
    haConnected: false,
    additionalItems: [
      {
        label: 'Add Sample Tile',
        icon: 'science',
        action: () => this.addSampleTile(),
        divider: false
      }
    ]
  };
  
  // Track for undo functionality
  private lastSavedState: DashboardItem[] | null = null;
  private undoStack: DashboardSnapshot[] = [];
  canUndo = false;
  
  private subscriptions = new Subscription();
  
  haConnected = false;
  
  // Available tile types for the add tile dialog
  tileTypes = [
    { id: 'device-status', name: 'Device Status', icon: 'power_settings_new' },
    { id: 'entity-browser', name: 'Entity Browser', icon: 'list' },
    { id: 'device-browser', name: 'Device Browser', icon: 'devices' },
    { id: 'floorplan', name: 'Floorplan', icon: 'map' },
    // Add more tile types as needed
  ];
  
  // Map to track locked tiles
  lockedTiles: Map<string, boolean> = new Map<string, boolean>();
  
  constructor(
    private dashboardLoaderService: DashboardLoaderService,
    private dialog: MatDialog,
    private homeAssistantService: HomeAssistantService,
    private themeService: ThemeService,
    private notificationService: NotificationService
  ) {
    // Subscribe to Home Assistant connection status
    this.homeAssistantService.connectionStatus$.subscribe(connected => {
      this.haConnected = connected;
    });
  }
  
  @HostListener('window:resize')
  onResize(): void {
    // This will force the grid to recalculate layout when window size changes
    if (this.options.api && typeof this.options.api.resize === 'function') {
      this.options.api.resize();
    }
  }
  
  ngOnInit(): void {
    // Start with draggable and resizable disabled until we know the actual state
    this.options.draggable = {
      ...this.options.draggable,
      enabled: false
    };
    
    this.options.resizable = {
      ...this.options.resizable,
      enabled: false
    };
    
    // Create a copy of options to ensure changes are applied
    this.options = { ...this.options };
    
    // Load the dashboard (which will set the proper editing state)
    this.loadDashboard();
    
    // Setup autosave with debounce
    const autosaveSubscription = this.changesSubject.pipe(
      debounceTime(3000), // Wait for 3 seconds of inactivity
      tap(() => {
        if (this.autoSaveEnabled && this.dashboard?.id) {
          this.saveLayout(true);
        }
      })
    ).subscribe();
    
    this.subscriptions.add(autosaveSubscription);
    
    // Subscribe to Home Assistant connection status
    this.subscriptions.add(
      this.homeAssistantService.connectionStatus$.subscribe(connected => {
        this.haConnected = connected;
        this.updateMenuConfig();
      })
    );
  }
  
  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }
  
  itemChange(item: GridsterItem, itemComponent: unknown): void {
    console.log('itemChanged', item, itemComponent);
    this.triggerAutosave();
    this.onItemChange(this.items);
  }

  itemResize(item: GridsterItem, itemComponent: unknown): void {
    console.log('itemResized', item, itemComponent);
    this.triggerAutosave();
  }
  
  /**
   * Trigger the autosave mechanism
   */
  private triggerAutosave(): void {
    this.changesSubject.next();
  }
  
  /**
   * Show a notification message
   */
  private showStatusMessage(message: string, duration = 3000): void {
    if (message.toLowerCase().includes('error')) {
      this.notificationService.error(message, duration);
    } else if (message.toLowerCase().includes('success')) {
      this.notificationService.success(message, duration);
    } else {
      this.notificationService.info(message, duration);
    }
  }
  
  loadDashboard(): void {
    this.isLoading = true;
    this.error = null;
    
    // First try to get the default dashboard
    const dashboardSubscription = this.dashboardLoaderService.getDefaultDashboard()
      .pipe(
        map(defaultDashboard => {
          this.isLoading = false;
          
          if (!defaultDashboard) {
            // If no default dashboard exists, check if there are any dashboards
            return this.dashboardLoaderService.getDashboards().pipe(
              map(dashboards => {
                if (dashboards.length === 0) {
                  // Create a new dashboard if none exists
                  this.showStatusMessage("No dashboards found. Creating a new one...");
                  this.createNewDashboard();
                  return null;
                }
                
                // Load the first dashboard
                return dashboards[0];
              })
            );
          }
          
          return of(defaultDashboard);
        }),
        catchError(error => {
          this.isLoading = false;
          this.error = 'Failed to load dashboards. ' + error.message;
          this.showStatusMessage("Error loading dashboard", 5000);
          return of(null);
        })
      )
      .subscribe(dashboardOrObservable => {
        if (dashboardOrObservable instanceof Observable) {
          // If it's an observable, subscribe to it
          const innerSubscription = dashboardOrObservable.subscribe(innerDashboard => {
            if (innerDashboard) {
              this.setupDashboard(innerDashboard);
              this.showStatusMessage("Dashboard loaded successfully", 2000);
            }
          });
          this.subscriptions.add(innerSubscription);
        } else if (dashboardOrObservable) {
          // If it's a dashboard, set it up directly
          this.setupDashboard(dashboardOrObservable);
          this.showStatusMessage("Dashboard loaded successfully", 2000);
        }
      });
      
    this.subscriptions.add(dashboardSubscription);
  }
  
  /**
   * Refreshes the gridster grid to apply changes to options
   */
  private refreshGridsterOptions(): void {
    // Double-check that draggable and resizable match the current editing state
    if (this.options.draggable) {
      this.options.draggable.enabled = this.isEditingEnabled;
    }
    
    if (this.options.resizable) {
      this.options.resizable.enabled = this.isEditingEnabled;
    }
    
    // First create a fresh copy of options
    this.options = { ...this.options };
    
    // Then apply changes using the API
    setTimeout(() => {
      if (this.options.api) {
        if (typeof this.options.api.optionsChanged === 'function') {
          console.log('Applying gridster options with editing:', this.isEditingEnabled);
          this.options.api.optionsChanged();
        }
        
        if (typeof this.options.api.resize === 'function') {
          this.options.api.resize();
        }
      }
    }, 0);
  }
  
  private setupDashboard(dashboard: Dashboard): void {
    this.dashboard = dashboard;
    this.items = dashboard.items || [];
    this.lastSavedState = JSON.parse(JSON.stringify(this.items)); // Store initial state
    
    // Load the editing state if it exists
    if (dashboard.isEditingEnabled !== undefined) {
      this.isEditingEnabled = dashboard.isEditingEnabled;
    }
    
    console.log(`Dashboard "${dashboard.name}" loaded with editing ${this.isEditingEnabled ? 'enabled' : 'disabled'}`);
    
    // Update menu config
    this.menuConfig = {
      autoSaveEnabled: this.autoSaveEnabled,
      isDefault: dashboard.isDefault || false,
      showGrid: this.options.displayGrid !== DisplayGrid.None,
      canUndo: false,
      isSaving: false,
      dashboardName: dashboard.name || 'Dashboard',
      isEditingEnabled: this.isEditingEnabled,
      haConnected: this.haConnected
    };
    
    // Apply dragging and resizing according to editing state
    this.options.draggable = {
      ...this.options.draggable,
      enabled: this.isEditingEnabled
    };
    
    this.options.resizable = {
      ...this.options.resizable,
      enabled: this.isEditingEnabled
    };
    
    // Create a fresh copy of options to ensure changes are applied
    this.options = { ...this.options };
    
    // Apply custom config if available
    if (dashboard.gridsterConfig) {
      // Create a new clean config without the serialized properties that might cause issues
      const cleanConfig: GridsterConfig = {
        ...this.options,
        // These properties won't have functions when loaded from Firestore
        // so make sure we use our instance's callbacks
        itemChangeCallback: this.itemChange.bind(this),
        itemResizeCallback: this.itemResize.bind(this)
      };
      
      // Copy non-function properties from the stored config
      const gridsterConfig = dashboard.gridsterConfig;
      Object.keys(gridsterConfig).forEach(key => {
        // Skip known function properties
        if (key !== 'itemChangeCallback' && 
            key !== 'itemResizeCallback' && 
            key !== 'itemInitCallback' && 
            key !== 'itemRemovedCallback' &&
            key !== 'gridInit' &&
            key !== 'gridDestroy' &&
            key !== 'gridSizeChanged' &&
            key !== 'itemValidateCallback') {
          // Copy the property, but be careful with draggable and resizable
          if (key === 'draggable' || key === 'resizable') {
            // Keep original draggable/resizable config but update enabled state
            const originalObj = this.options[key] || {};
            const loadedObj = gridsterConfig[key] || {};
            
            if (key === 'draggable') {
              cleanConfig.draggable = {
                ...originalObj,
                enabled: this.isEditingEnabled // Force to respect current edit state
              };
            } else if (key === 'resizable') {
              cleanConfig.resizable = {
                ...originalObj,
                enabled: this.isEditingEnabled // Force to respect current edit state
              };
            }
          } else {
            // For other properties, copy directly
            (cleanConfig as Record<string, unknown>)[key] = gridsterConfig[key];
          }
        }
      });
      
      // Update options with the clean config
      this.options = cleanConfig;
    }
    
    // Ensure grid is refreshed with a slight delay to allow for initialization
    setTimeout(() => {
      this.refreshGridsterOptions();
    }, 100);
    
    // Load snapshots for undo/redo functionality
    if (dashboard.id) {
      const snapshotsSubscription = this.dashboardLoaderService.getDashboardSnapshots(dashboard.id)
        .subscribe(snapshots => {
          this.snapshots = snapshots;
          this.canUndo = snapshots.length > 0;
          this.undoStack = [...snapshots];
          this.updateMenuConfig();
        });
      this.subscriptions.add(snapshotsSubscription);
    }
  }
  
  createNewDashboard(): void {
    // Create floorplan tile that takes full height and half width
    const floorplanTile: FloorplanTile = { 
      cols: 6, // Half width (6 columns of 12)
      rows: 12, // Full height
      y: 0, 
      x: 0, 
      type: 'floorplan', 
      id: 'floorplan',
      imagePath: '/floorplan.svg',
      title: 'Floor Plan',
      interactive: true
    };
    
    // Create some other example tiles
    const otherTiles: DashboardItem[] = [
      { 
        cols: 6, 
        rows: 4, 
        y: 0, 
        x: 6, 
        type: 'default', 
        id: 'item1',
        title: 'Status Overview'
      },
      { 
        cols: 6, 
        rows: 4, 
        y: 4, 
        x: 6, 
        type: 'default', 
        id: 'item2',
        title: 'Controls'
      },
      { 
        cols: 6, 
        rows: 4, 
        y: 8, 
        x: 6, 
        type: 'default', 
        id: 'item3',
        title: 'Analytics'
      }
    ];
    
    // Combine all tiles
    const defaultItems: DashboardItem[] = [
      floorplanTile,
      ...otherTiles
    ];
    
    // Create the new dashboard object
    const newDashboard: Dashboard = {
      name: 'Home Dashboard',
      items: defaultItems,
      gridsterConfig: this.options,
      description: 'Default home dashboard with floorplan',
      isDefault: true,
      tags: ['default', 'home', 'floorplan'],
      isEditingEnabled: true // Start in editing mode by default
    };
    
    this.isLoading = true;
    this.error = null; // Clear any previous errors
    this.showStatusMessage("Creating new dashboard...");
    
    // Check if we should mark this as default (we should if no dashboards exist)
    this.dashboardLoaderService.getDashboards().pipe(
      take(1),
      switchMap(dashboards => {
        // If this is the first dashboard, mark it as default
        if (dashboards.length === 0) {
          newDashboard.isDefault = true;
        }
        return this.dashboardLoaderService.createDashboard(newDashboard);
      }),
      catchError(error => {
        this.isLoading = false;
        this.error = 'Failed to create dashboard. ' + error.message;
        this.showStatusMessage("Error creating dashboard", 5000);
        return of('');
      })
    ).subscribe(id => {
      this.isLoading = false;
      if (id) {
        this.dashboard = { ...newDashboard, id };
        this.items = defaultItems;
        this.lastSavedState = JSON.parse(JSON.stringify(this.items));
        this.showStatusMessage("New dashboard created successfully", 3000);
        
        // Update menu config
        this.menuConfig = {
          autoSaveEnabled: this.autoSaveEnabled,
          isDefault: newDashboard.isDefault,
          showGrid: this.options.displayGrid !== DisplayGrid.None,
          canUndo: false,
          isSaving: false,
          dashboardName: newDashboard.name || 'Dashboard',
          isEditingEnabled: this.isEditingEnabled,
          haConnected: this.haConnected
        };
        
        // If this is the first dashboard, mark it as default
        if (newDashboard.isDefault) {
          console.log('Setting newly created dashboard as default');
          // No need to call setAsDefault since we already marked it as default during creation
        }
      } else {
        // Handle the case where id is empty but no error was thrown
        this.error = 'Failed to create dashboard. Please try again.';
        this.showStatusMessage("Error creating dashboard", 5000);
      }
    });
  }
  
  saveLayout(isAutosave = false): void {
    if (!this.dashboard?.id) {
      this.error = 'No dashboard loaded to save';
      return;
    }
    
    // Skip saving if items haven't changed
    if (this.itemsEqual(this.items, this.lastSavedState)) {
      console.log('No changes to save');
      return;
    }
    
    this.isSaving = true;
    this.updateMenuConfig();
    
    if (!isAutosave) {
      this.showStatusMessage("Saving dashboard layout...");
    }
    
    const saveSubscription = this.dashboardLoaderService.saveDashboardLayout(
      this.dashboard.id, 
      this.items,
      this.options
    )
    .pipe(
      catchError(error => {
        this.isSaving = false;
        this.error = 'Failed to save layout. ' + error.message;
        this.showStatusMessage("Error saving dashboard layout", 5000);
        return of(void 0);
      })
    )
    .subscribe(() => {
      this.isSaving = false;
      this.updateMenuConfig();
      this.lastSavedState = JSON.parse(JSON.stringify(this.items));
      if (isAutosave) {
        console.log('Dashboard layout autosaved successfully');
      } else {
        console.log('Dashboard layout saved successfully');
        this.showStatusMessage("Dashboard layout saved successfully", 2000);
      }
      
      // Refresh snapshots after saving
      if (this.dashboard?.id) {
        this.refreshSnapshots(this.dashboard.id);
      }
    });
    
    this.subscriptions.add(saveSubscription);
  }
  
  /**
   * Compare two item arrays to see if they're equal
   */
  private itemsEqual(items1: DashboardItem[] | null, items2: DashboardItem[] | null): boolean {
    if (!items1 || !items2) {
      return items1 === items2;
    }
    
    if (items1.length !== items2.length) {
      return false;
    }
    
    // Make deep copies to compare the actual values
    const copy1 = JSON.stringify(items1);
    const copy2 = JSON.stringify(items2);
    
    return copy1 === copy2;
  }
  
  /**
   * Refresh the snapshots list
   */
  private refreshSnapshots(dashboardId: string): void {
    const snapshotsSubscription = this.dashboardLoaderService.getDashboardSnapshots(dashboardId)
      .subscribe(snapshots => {
        this.snapshots = snapshots;
        this.canUndo = snapshots.length > 0;
        this.undoStack = [...snapshots];
        this.updateMenuConfig();
      });
    
    this.subscriptions.add(snapshotsSubscription);
  }
  
  onItemChange(items: DashboardItem[]): void {
    // Update items when changes happen
    this.items = items;
  }
  
  /**
   * Get the type of a tile for rendering
   */
  getTileType(item: DashboardItem): TileType {
    if (!item) return 'default';
    
    const validTypes: TileType[] = [
      'floorplan', 
      'default', 
      'weather', 
      'camera', 
      'device', 
      'sensor', 
      'chart', 
      'custom', 
      'device-status', 
      'entity-browser', 
      'device-browser',
      'sample'
    ];
    
    return validTypes.includes(item.type as TileType) ? (item.type as TileType) : 'default';
  }
  
  /**
   * Get the device type from a dashboard item's settings
   */
  getDeviceType(item: DashboardItem): 'light' | 'sensor' | 'switch' | 'climate' | 'media_player' | 'camera' {
    if (!item || !item.settings) {
      return 'light'; // Default
    }
    
    const deviceType = item.settings['deviceType'];
    
    if (typeof deviceType === 'string') {
      switch (deviceType) {
        case 'light':
        case 'sensor':
        case 'switch':
        case 'climate':
        case 'media_player':
        case 'camera':
          return deviceType as any;
        default:
          return 'light';
      }
    }
    
    return 'light';
  }
  
  /**
   * Get a string value from an item's settings safely
   */
  getStringFromSettings(item: DashboardItem, key: string): string | undefined {
    if (!item || !item.settings) {
      return undefined;
    }
    
    const value = item.settings[key];
    if (value !== undefined && typeof value === 'string') {
      return value;
    }
    
    return undefined;
  }
  
  /**
   * Converts a DashboardItem to a FloorplanTile
   * This is needed because we can't use the "as" operator in the template
   */
  convertToFloorplanTile(item: DashboardItem): FloorplanTile {
    // If it's already a FloorplanTile with all required properties, return it
    if (isFloorplanTile(item)) {
      return item;
    }
    
    // Otherwise, create a new FloorplanTile with default values
    return {
      ...item,
      type: 'floorplan', 
      imagePath: item['imagePath'] || '/floorplan.svg',
      title: item['title'] || 'Floor Plan',
      interactive: item['interactive'] || false,
      markers: item['markers'] || []
    };
  }
  
  /**
   * Undo the last change by restoring the most recent snapshot
   */
  undoLastChange(): void {
    if (!this.canUndo || this.snapshots.length === 0 || !this.dashboard?.id) {
      this.showStatusMessage("Nothing to undo", 2000);
      return;
    }
    
    // Get the most recent snapshot
    const mostRecentSnapshot = this.snapshots[0];
    
    // Check if the snapshot has an id (we added this when fetching from Firebase)
    if (!('id' in mostRecentSnapshot)) {
      this.showStatusMessage("Cannot undo - invalid snapshot", 2000);
      return;
    }
    
    this.showStatusMessage("Undoing last change...");
    this.isLoading = true;
    this.updateMenuConfig();
    
    const restoreSubscription = this.dashboardLoaderService.restoreSnapshot(mostRecentSnapshot.id as string)
      .pipe(
        catchError(error => {
          this.isLoading = false;
          this.error = 'Failed to undo. ' + error.message;
          this.showStatusMessage("Error undoing last change", 5000);
          return of(void 0);
        }),
        switchMap(() => {
          // Reload the dashboard after restoration
          return this.dashboard?.id 
            ? this.dashboardLoaderService.getDashboard(this.dashboard.id) 
            : of(null);
        })
      )
      .subscribe(dashboard => {
        this.isLoading = false;
        if (dashboard) {
          this.setupDashboard(dashboard);
          this.showStatusMessage("Last change undone successfully", 2000);
        }
        this.updateMenuConfig();
      });
      
    this.subscriptions.add(restoreSubscription);
  }
  
  // Method to set current dashboard as default
  setAsDefault(): void {
    if (!this.dashboard?.id) {
      this.error = 'No dashboard loaded to set as default';
      return;
    }
    
    this.showStatusMessage("Setting as default dashboard...");
    const defaultSubscription = this.dashboardLoaderService.setAsDefault(this.dashboard.id)
      .pipe(
        catchError(error => {
          this.error = 'Failed to set as default. ' + error.message;
          this.showStatusMessage("Error setting as default", 5000);
          return of(void 0);
        })
      )
      .subscribe(() => {
        console.log('Dashboard set as default successfully');
        this.showStatusMessage("Set as default dashboard successfully", 2000);
        if (this.dashboard) {
          this.dashboard.isDefault = true;
          this.menuConfig.isDefault = true;
        }
      });
      
    this.subscriptions.add(defaultSubscription);
  }
  
  /**
   * Toggle autosave functionality
   */
  toggleAutosave(enabled?: boolean): void {
    if (enabled !== undefined) {
      this.autoSaveEnabled = enabled;
    } else {
      this.autoSaveEnabled = !this.autoSaveEnabled;
    }
    
    this.menuConfig.autoSaveEnabled = this.autoSaveEnabled;
    
    this.showStatusMessage(
      this.autoSaveEnabled ? "Autosave enabled" : "Autosave disabled", 
      2000
    );
  }
  
  /**
   * Toggle grid display
   */
  toggleGridDisplay(show?: boolean): void {
    if (show !== undefined) {
      this.options.displayGrid = show ? DisplayGrid.Always : DisplayGrid.None;
    } else {
      this.options.displayGrid = 
        this.options.displayGrid === DisplayGrid.None 
          ? DisplayGrid.Always 
          : DisplayGrid.None;
    }
    
    this.menuConfig.showGrid = this.options.displayGrid !== DisplayGrid.None;
    
    // Update the grid
    this.refreshGridsterOptions();
    
    this.showStatusMessage(
      this.menuConfig.showGrid ? "Grid display enabled" : "Grid display disabled", 
      2000
    );
  }
  
  /**
   * Updates the menu configuration object
   */
  private updateMenuConfig(): void {
    this.menuConfig = {
      ...this.menuConfig,
      autoSaveEnabled: this.autoSaveEnabled,
      isDefault: this.dashboard?.isDefault || false,
      showGrid: this.options.displayGrid !== DisplayGrid.None,
      canUndo: this.canUndo,
      isSaving: this.isSaving,
      dashboardName: this.dashboard?.name || 'Dashboard',
      isEditingEnabled: this.isEditingEnabled,
      haConnected: this.haConnected
    };
  }
  
  /**
   * Remove a tile from the dashboard
   */
  removeTile(item: GridsterItem): void {
    // Find the item index
    const index = this.items.findIndex(i => 
      i.x === item.x && 
      i.y === item.y && 
      i.cols === item.cols && 
      i.rows === item.rows
    );
    
    if (index !== -1) {
      const tileToRemove = this.items[index];
      const tileType = tileToRemove.type || 'default';
      const tileTitle = tileToRemove.title || `Tile ${tileToRemove.id || ''}`;
      
      // Remove the item
      this.items.splice(index, 1);
      
      // Trigger autosave
      this.triggerAutosave();
      
      // Show status message
      this.showStatusMessage(`Removed ${tileTitle} (${tileType} tile)`, 3000);
      console.log(`Removed tile:`, tileToRemove);
    }
  }
  
  /**
   * Toggle editing mode (lock/unlock the dashboard)
   */
  toggleEditMode(): void {
    this.isEditingEnabled = !this.isEditingEnabled;
    
    // Update draggable/resizable config
    if (this.options.draggable) {
      this.options.draggable.enabled = this.isEditingEnabled;
    }
    if (this.options.resizable) {
      this.options.resizable.enabled = this.isEditingEnabled;
    }
    
    // Update the dashboard configuration
    if (this.dashboard) {
      this.dashboard.isEditingEnabled = this.isEditingEnabled;
    }
    
    // Refresh gridster to apply the new settings
    this.refreshGridsterOptions();
    
    // Update locked state for individually locked tiles
    this.applyLockedTilesConfig();
    
    // Autosave if enabled
    this.triggerAutosave();
  }
  
  /**
   * Opens the Home Assistant configuration dialog
   */
  openHAConfig(): void {
    const dialogRef = this.dialog.open(HAConfigComponent, {
      width: '500px',
      disableClose: false
    });
    
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        console.log('Home Assistant configuration updated');
      }
    });
  }
  
  /**
   * Opens the Add Tile dialog and handles the result
   */
  openAddTileDialog(): void {
    if (!this.isEditingEnabled) {
      this.showStatusMessage('Dashboard is locked. Unlock it to add tiles.', 3000);
      return;
    }
    
    const dialogRef = this.dialog.open(AddTileDialogComponent, {
      width: '600px',
      disableClose: false
    });
    
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.addNewTile(result.type, result.title, result.settings);
      }
    });
  }
  
  /**
   * Add a new tile to the dashboard
   */
  addNewTile(tileType: TileType, title?: string, settings?: Record<string, any>): void {
    if (!this.dashboard) {
      this.showStatusMessage('No active dashboard', 3000);
      return;
    }
    
    // Generate a unique ID for the new tile
    const tileId = `tile_${Math.random().toString(36).substring(2, 11)}`;
    
    // Check if we have a deviceType in settings for device-status tiles
    const deviceType = tileType === 'device-status' ? (settings?.['deviceType'] || 'light') : null;
    
    // Create a new tile with default properties based on type
    let newTile: DashboardItem = {
      id: tileId,
      type: tileType,
      title: title || `New ${tileType} Tile`,
      cols: 4,
      rows: 4,
      x: 0,
      y: 0,
      settings: settings || {}
    };
    
    // Set type-specific properties
    switch (tileType) {
      case 'floorplan':
        newTile = {
          ...newTile,
          imagePath: '/floorplan.svg',
          interactive: true,
          cols: 6,
          rows: 8
        } as FloorplanTile;
        break;
      case 'device-status':
        newTile = {
          ...newTile,
          cols: deviceType === 'camera' ? 6 : 4,
          rows: deviceType === 'camera' ? 4 : 4,
          settings: {
            ...newTile.settings,
            deviceType: deviceType,
            refreshInterval: 30000 // 30 seconds refresh
          }
        };
        break;
      case 'entity-browser':
        newTile = {
          ...newTile,
          cols: 8,
          rows: 8,
          title: title || 'Home Assistant Entities'
        };
        break;
      case 'weather':
        newTile.cols = 4;
        newTile.rows = 4;
        break;
      case 'camera':
        newTile.cols = 6;
        newTile.rows = 4;
        break;
      case 'device':
        newTile.cols = 2;
        newTile.rows = 2;
        break;
      case 'sensor':
        newTile.cols = 2;
        newTile.rows = 2;
        break;
      case 'chart':
        newTile.cols = 6;
        newTile.rows = 4;
        break;
      default:
        // Default size for other types
        newTile.cols = 4;
        newTile.rows = 4;
    }
    
    // Add the new tile to the dashboard
    this.items = [...this.items, newTile];
    
    // Trigger autosave if enabled
    this.triggerAutosave();
    
    // Show success message
    this.showStatusMessage(`Added new ${title || tileType} tile`, 3000);
  }

  /**
   * Opens the Home Assistant explorer modal
   */
  openHAExplorer(): void {
    if (!this.haConnected) {
      this.showStatusMessage('Home Assistant is not connected. Please check your connection.', 3000);
      return;
    }

    const dialogRef = this.dialog.open(HaExplorerModalComponent, {
      width: '90vw',
      maxWidth: '1200px',
      height: '90vh',
      maxHeight: '90vh',
      panelClass: 'ha-explorer-dialog',
      disableClose: false
    });

    dialogRef.afterClosed().subscribe(result => {
      console.log('HA Explorer modal closed');
    });
  }

  /**
   * Called when gridster is initialized
   */
  onGridInit(): void {
    console.log('Gridster initialized with editing mode:', this.isEditingEnabled);
    
    // Ensure editing state is applied
    if (this.options.draggable) {
      this.options.draggable.enabled = this.isEditingEnabled;
    }
    
    if (this.options.resizable) {
      this.options.resizable.enabled = this.isEditingEnabled;
    }
    
    // Apply the changes
    this.refreshGridsterOptions();
  }

  /**
   * Check if a tile is locked
   */
  isTileLocked(item: DashboardItem): boolean {
    return this.lockedTiles.get(item.id) === true;
  }
  
  /**
   * Handle tile lock changes
   */
  onTileLockChange(event: {item: DashboardItem, locked: boolean}): void {
    this.lockedTiles.set(event.item.id, event.locked);
    
    // Update draggability for this specific item
    if (event.locked) {
      // Find the gridster item component and disable drag
      const gridsterItem = this.getGridsterItemComponent(event.item);
      if (gridsterItem) {
        gridsterItem.itemComponent.dragEnabled = false;
        gridsterItem.itemComponent.resizeEnabled = false;
      }
    } else {
      // Find the gridster item component and enable drag if editing is enabled
      const gridsterItem = this.getGridsterItemComponent(event.item);
      if (gridsterItem && this.isEditingEnabled) {
        gridsterItem.itemComponent.dragEnabled = true;
        gridsterItem.itemComponent.resizeEnabled = true;
      }
    }
    
    // Save the dashboard to persist lock state
    this.triggerAutosave();
  }
  
  /**
   * Get the gridster item component for a given item (helper method)
   * Note: This is a simplified approach and might need adjustment based on your component structure
   */
  private getGridsterItemComponent(item: DashboardItem): any {
    // In a real implementation, you'd need to get a reference to the gridster item component
    // This might involve ViewChildren or a service to track gridster items
    return null;
  }
  
  /**
   * Apply locked tiles configuration to gridster items
   */
  private applyLockedTilesConfig(): void {
    // This would be implemented depending on your gridster integration
    // For each locked tile, find its gridster item component and disable drag/resize
  }

  /**
   * Add a sample tile for testing
   */
  addSampleTile(): void {
    const newTile: DashboardItem = {
      id: `sample-${Date.now()}`,
      type: 'sample',
      title: 'Sample Tile',
      cols: 3,
      rows: 3,
      x: 0,
      y: 0,
      settings: {
        sampleData: 'This is a sample tile for testing purposes'
      }
    };
    
    this.items.push(newTile);
    this.triggerAutosave();
    this.showStatusMessage('Sample tile added to the dashboard.', 3000);
  }
}
