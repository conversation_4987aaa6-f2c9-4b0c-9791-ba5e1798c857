import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HttpClientModule } from '@angular/common/http';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

// Angular Material modules
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

// Tile components
import { DashboardTilesComponent } from './dashboard-tiles/dashboard-tiles.component';
import { BaseTileComponent } from './tiles/base-tile/base-tile.component';
import { SampleTileComponent } from './tiles/sample-tile/sample-tile.component';

// Shared components
import { LoadingSpinnerComponent } from './shared/components/loading-spinner/loading-spinner.component';
import { EmptyStateComponent } from './shared/components/empty-state/empty-state.component';

// Shared pipes
import { SafeHtmlPipe } from './shared/pipes/safe-html.pipe';

// Services
import { NotificationService } from './shared/services/notification.service';
import { SvgHelperService } from './shared/services/svg-helper.service';

@NgModule({
  imports: [
    CommonModule,
    HttpClientModule,
    FormsModule,
    ReactiveFormsModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatProgressSpinnerModule,
    // Import standalone components
    DashboardTilesComponent,
    BaseTileComponent,
    SampleTileComponent,
    LoadingSpinnerComponent,
    EmptyStateComponent,
    SafeHtmlPipe
  ],
  providers: [
    NotificationService,
    SvgHelperService
  ],
  exports: [
    // Export all components and pipes
    DashboardTilesComponent,
    BaseTileComponent,
    SampleTileComponent,
    LoadingSpinnerComponent,
    EmptyStateComponent,
    SafeHtmlPipe,
    // Re-export common modules for convenience
    CommonModule,
    HttpClientModule,
    FormsModule,
    ReactiveFormsModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatProgressSpinnerModule
  ]
})
export class DashboardTilesModule {}
