import { GridsterConfig, GridsterItem } from 'angular-gridster2';

/**
 * Base dashboard model representing a dashboard saved in Firebase
 */
export interface Dashboard {
  id?: string;
  name: string;
  items: DashboardItem[];
  gridsterConfig?: Record<string, unknown>;
  createdAt?: Date;
  updatedAt?: Date;
  userId?: string;
  description?: string;
  isDefault?: boolean;
  tags?: string[];
  isEditingEnabled?: boolean;
}

/**
 * A serializable version of GridsterConfig that doesn't include functions
 * This ensures we can safely store it in Firestore
 */
export type SerializableGridsterConfig = Omit<GridsterConfig, 
  'itemChangeCallback' | 'itemResizeCallback' | 'itemInitCallback' | 'itemRemovedCallback' | 
  'gridInit' | 'gridDestroy' | 'gridSizeChanged' | 'itemValidateCallback' | 'draggable' | 'resizable'> & {
    draggable?: {
      enabled?: boolean;
      ignoreContent?: boolean;
      ignoreContentClass?: string;
      dragHandleClass?: string;
      stop?: boolean;
      start?: boolean;
      dropOverItems?: boolean;
      dropOverItemsCallback?: boolean;
    };
    resizable?: {
      enabled?: boolean;
      handles?: {
        s?: boolean;
        e?: boolean;
        n?: boolean;
        w?: boolean;
        se?: boolean;
        ne?: boolean;
        sw?: boolean;
        nw?: boolean;
      };
      stop?: boolean;
      start?: boolean;
    };
};

/**
 * Base interface for all dashboard items/tiles
 * Extends GridsterItem with common properties
 */
export interface DashboardItem extends GridsterItem {
  id: string;
  type: TileType;
  title?: string;
  icon?: string;
  settings?: Record<string, unknown>;
}

/**
 * Supported dashboard tile types
 */
export type TileType = 'floorplan' | 'default' | 'weather' | 'camera' | 'device' | 'sensor' | 'chart' | 'custom' | 'device-status' | 'entity-browser' | 'device-browser' | 'sample';

/**
 * Room configuration for the floorplan
 */
export interface Room {
  id: string;
  name: string;
  type: 'room' | 'hallway' | 'bathroom' | 'kitchen' | 'bedroom' | 'living' | 'other';
  imagePath?: string;
  description?: string;
  devices?: HADeviceMapping[];
  isCustomShape?: boolean;
  customPolygon?: Point[];
  isDetected?: boolean; // Whether this room was auto-detected
  detectionConfidence?: number; // Confidence score from detection algorithm
}

/**
 * Point definition for custom polygon shapes
 */
export interface Point {
  x: number;
  y: number;
}

/**
 * Extended FloorplanTile with rooms mapping
 */
export interface FloorplanTile extends DashboardItem {
  type: 'floorplan';
  imagePath: string;
  svgContent?: string; // Stores the SVG content with highlighted detected rooms
  scale?: number;
  markers?: FloorplanMarker[];
  interactive?: boolean;
  rooms?: { [id: string]: Room };
  // Home Assistant integration
  haDevices?: HADeviceMapping[];
  showDeviceStates?: boolean;
  refreshInterval?: number; // in milliseconds
  // Custom room creation mode
  isRoomEditMode?: boolean;
}

/**
 * Marker for interactive points on a floorplan
 */
export interface FloorplanMarker {
  id: string;
  x: number;
  y: number;
  type: 'light' | 'sensor' | 'device' | 'door' | 'window' | 'custom';
  label?: string;
  deviceId?: string;
  color?: string;
  icon?: string;
  actions?: MarkerAction[];
  // Home Assistant integration
  entityId?: string;
  showState?: boolean;
  stateAttribute?: string;
}

/**
 * Actions that can be performed when clicking on markers
 */
export interface MarkerAction {
  id: string;
  name: string;
  type: 'toggle' | 'dim' | 'navigate' | 'custom';
  endpoint?: string;
  payload?: Record<string, unknown>;
}

/**
 * Weather tile specific properties
 */
export interface WeatherTile extends DashboardItem {
  type: 'weather';
  location?: string;
  showForecast?: boolean;
  units?: 'metric' | 'imperial';
  apiKey?: string;
  refreshInterval?: number;
}

/**
 * Camera tile specific properties
 */
export interface CameraTile extends DashboardItem {
  type: 'camera';
  streamUrl?: string;
  snapshotUrl?: string;
  refreshInterval?: number;
  showControls?: boolean;
}

/**
 * Device control tile properties
 */
export interface DeviceTile extends DashboardItem {
  type: 'device';
  deviceId: string;
  deviceType?: 'switch' | 'light' | 'thermostat' | 'fan' | 'cover' | 'lock' | 'custom';
  showStatus?: boolean;
  showControls?: boolean;
  customCommand?: string;
}

/**
 * Sensor display tile properties
 */
export interface SensorTile extends DashboardItem {
  type: 'sensor';
  sensorId: string;
  sensorType?: 'temperature' | 'humidity' | 'pressure' | 'light' | 'motion' | 'contact' | 'custom';
  unit?: string;
  minValue?: number;
  maxValue?: number;
  showChart?: boolean;
  refreshInterval?: number;
}

/**
 * Chart/graph tile properties
 */
export interface ChartTile extends DashboardItem {
  type: 'chart';
  chartType: 'line' | 'bar' | 'pie' | 'radar';
  dataSource: string;
  labels?: string[];
  series?: unknown[];
  timeRange?: 'hour' | 'day' | 'week' | 'month' | 'custom';
  customTimeRange?: { start: Date; end: Date };
  refreshInterval?: number;
  showLegend?: boolean;
}

/**
 * Custom tile for user-defined components
 */
export interface CustomTile extends DashboardItem {
  type: 'custom';
  componentName: string;
  componentProps?: Record<string, unknown>;
}

/**
 * Dashboard snapshot - for undo/redo functionality
 */
export interface DashboardSnapshot {
  timestamp: Date;
  dashboardId: string;
  items: DashboardItem[];
  gridsterConfig?: Record<string, unknown>;
  id?: string; // Added for Firebase document ID
}

/**
 * Type guard to check if an item is a floorplan tile
 */
export function isFloorplanTile(item: DashboardItem): item is FloorplanTile {
  return item.type === 'floorplan';
}

/**
 * Type guard to check if an item is a weather tile
 */
export function isWeatherTile(item: DashboardItem): item is WeatherTile {
  return item.type === 'weather';
}

/**
 * Type guard to check if an item is a camera tile
 */
export function isCameraTile(item: DashboardItem): item is CameraTile {
  return item.type === 'camera';
}

/**
 * Type guard to check if an item is a device tile
 */
export function isDeviceTile(item: DashboardItem): item is DeviceTile {
  return item.type === 'device';
}

/**
 * Type guard to check if an item is a sensor tile
 */
export function isSensorTile(item: DashboardItem): item is SensorTile {
  return item.type === 'sensor';
}

/**
 * Type guard to check if an item is a chart tile
 */
export function isChartTile(item: DashboardItem): item is ChartTile {
  return item.type === 'chart';
}

/**
 * Type guard to check if an item is a custom tile
 */
export function isCustomTile(item: DashboardItem): item is CustomTile {
  return item.type === 'custom';
}

/**
 * Home Assistant device mapping for floorplan
 */
export interface HADeviceMapping {
  roomId: string;
  entityId: string;
  position?: { x: number; y: number };
  markerType?: 'light' | 'sensor' | 'switch' | 'climate' | 'door' | 'window' | 'custom';
  showLabel?: boolean;
  showState?: boolean;
  stateAttribute?: string;
  icon?: string;
  color?: string;
}
