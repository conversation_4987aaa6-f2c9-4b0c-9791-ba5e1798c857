import type { Meta, StoryObj } from '@storybook/angular';
import { EmptyStateComponent } from './empty-state.component';

const meta: Meta<EmptyStateComponent> = {
  title: 'Dashboard Tiles/Shared/Empty State',
  component: EmptyStateComponent,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    title: {
      control: 'text',
      description: 'Title of the empty state'
    },
    description: {
      control: 'text',
      description: 'Description text'
    },
    icon: {
      control: 'text',
      description: 'CSS class for icon'
    },
    imageSrc: {
      control: 'text',
      description: 'Image source URL'
    },
    actionText: {
      control: 'text',
      description: 'Action button text'
    }
  },
};

export default meta;
type Story = StoryObj<EmptyStateComponent>;

export const Default: Story = {
  args: {
    title: 'No data available',
    description: undefined,
    icon: undefined,
    imageSrc: undefined,
    actionText: undefined
  },
  render: (args) => ({
    props: args,
    template: `
      <div style="width: 400px; height: 300px; border: 1px solid #ddd;">
        <app-empty-state 
          [title]="title"
          [description]="description"
          [icon]="icon"
          [imageSrc]="imageSrc"
          [actionText]="actionText">
        </app-empty-state>
      </div>
    `
  })
};

export const WithDescription: Story = {
  args: {
    title: 'No tiles configured',
    description: 'Get started by adding your first dashboard tile.',
    icon: undefined,
    imageSrc: undefined,
    actionText: undefined
  },
  render: (args) => ({
    props: args,
    template: `
      <div style="width: 400px; height: 300px; border: 1px solid #ddd;">
        <app-empty-state 
          [title]="title"
          [description]="description"
          [icon]="icon"
          [imageSrc]="imageSrc"
          [actionText]="actionText">
        </app-empty-state>
      </div>
    `
  })
};

export const WithIcon: Story = {
  args: {
    title: 'No data found',
    description: 'There are no items to display at this time.',
    icon: 'fas fa-inbox',
    imageSrc: undefined,
    actionText: undefined
  },
  render: (args) => ({
    props: args,
    template: `
      <div style="width: 400px; height: 300px; border: 1px solid #ddd;">
        <app-empty-state 
          [title]="title"
          [description]="description"
          [icon]="icon"
          [imageSrc]="imageSrc"
          [actionText]="actionText">
        </app-empty-state>
      </div>
    `
  })
};

export const WithAction: Story = {
  args: {
    title: 'No dashboard configured',
    description: 'Create your first dashboard to get started.',
    icon: 'fas fa-plus-circle',
    imageSrc: undefined,
    actionText: 'Create Dashboard'
  },
  render: (args) => ({
    props: args,
    template: `
      <div style="width: 400px; height: 300px; border: 1px solid #ddd;">
        <app-empty-state 
          [title]="title"
          [description]="description"
          [icon]="icon"
          [imageSrc]="imageSrc"
          [actionText]="actionText"
          (actionClick)="onActionClick()">
        </app-empty-state>
      </div>
    `,
    moduleMetadata: {
      providers: []
    }
  })
};
