import { Component, Input, EventEmitter, Output } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-empty-state',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="empty-state-container">
      <div class="empty-state-icon" *ngIf="icon">
        <i [class]="icon"></i>
      </div>
      <div class="empty-state-image" *ngIf="imageSrc">
        <img [src]="imageSrc" [alt]="title || 'Empty state'" />
      </div>
      <h3 class="empty-state-title">{{ title }}</h3>
      <p class="empty-state-description" *ngIf="description">{{ description }}</p>
      <div class="empty-state-action" *ngIf="actionText">
        <button (click)="actionClick.emit()" class="action-button">{{ actionText }}</button>
      </div>
    </div>
  `,
  styles: [`
    .empty-state-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;
      padding: 24px;
      height: 100%;
      box-sizing: border-box;
    }
    
    .empty-state-icon {
      font-size: 48px;
      color: #ccc;
      margin-bottom: 16px;
    }
    
    .empty-state-image {
      margin-bottom: 16px;
      max-width: 100%;
    }
    
    .empty-state-image img {
      max-width: 200px;
      max-height: 200px;
    }
    
    .empty-state-title {
      margin: 0 0 8px 0;
      color: #555;
      font-size: 18px;
      font-weight: 500;
    }
    
    .empty-state-description {
      margin: 0 0 16px 0;
      color: #777;
      font-size: 14px;
      max-width: 300px;
    }
    
    .empty-state-action {
      margin-top: 16px;
    }
    
    .action-button {
      padding: 8px 16px;
      background-color: #007bff;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.3s ease;
    }
    
    .action-button:hover {
      background-color: #0069d9;
    }
  `]
})
export class EmptyStateComponent {
  @Input() title = 'No data available';
  @Input() description?: string;
  @Input() icon?: string;
  @Input() imageSrc?: string;
  @Input() actionText?: string;
  @Output() actionClick = new EventEmitter<void>();
}
