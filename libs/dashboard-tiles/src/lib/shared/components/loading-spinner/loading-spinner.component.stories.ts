import type { Meta, StoryObj } from '@storybook/angular';
import { LoadingSpinnerComponent } from './loading-spinner.component';

const meta: Meta<LoadingSpinnerComponent> = {
  title: 'Dashboard Tiles/Shared/Loading Spinner',
  component: LoadingSpinnerComponent,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    overlay: {
      control: 'boolean',
      description: 'Whether to show as an overlay'
    },
    message: {
      control: 'text',
      description: 'Optional loading message'
    }
  },
};

export default meta;
type Story = StoryObj<LoadingSpinnerComponent>;

export const Default: Story = {
  args: {
    overlay: false,
    message: undefined
  }
};

export const WithMessage: Story = {
  args: {
    overlay: false,
    message: 'Loading dashboard...'
  }
};

export const Overlay: Story = {
  args: {
    overlay: true,
    message: 'Loading...'
  },
  render: (args) => ({
    props: args,
    template: `
      <div style="position: relative; width: 400px; height: 300px; background: #f5f5f5; border: 1px solid #ddd;">
        <div style="padding: 20px;">
          <h3>Content behind overlay</h3>
          <p>This content is behind the loading overlay.</p>
        </div>
        <app-loading-spinner 
          [overlay]="overlay"
          [message]="message">
        </app-loading-spinner>
      </div>
    `
  })
};
