import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';

export interface NotificationMessage {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  message: string;
  duration?: number;
  timestamp: Date;
}

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private notificationSubject = new Subject<NotificationMessage>();
  public notifications$ = this.notificationSubject.asObservable();

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  private showNotification(type: NotificationMessage['type'], message: string, duration = 5000): void {
    const notification: NotificationMessage = {
      id: this.generateId(),
      type,
      message,
      duration,
      timestamp: new Date()
    };

    this.notificationSubject.next(notification);
  }

  success(message: string, duration?: number): void {
    this.showNotification('success', message, duration);
  }

  error(message: string, duration?: number): void {
    this.showNotification('error', message, duration);
  }

  warning(message: string, duration?: number): void {
    this.showNotification('warning', message, duration);
  }

  info(message: string, duration?: number): void {
    this.showNotification('info', message, duration);
  }
}
