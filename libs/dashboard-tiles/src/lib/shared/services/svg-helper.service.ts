import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

export interface SvgRoomElement {
  id: string;
  element: SVGElement;
  bounds?: DOMRect;
}

@Injectable({
  providedIn: 'root'
})
export class SvgHelperService {

  constructor(private http: HttpClient) {}

  /**
   * Load SVG content from a URL
   */
  loadSvgFromUrl(url: string): Observable<string | null> {
    return this.http.get(url, { responseType: 'text' }).pipe(
      map(svgContent => this.optimizeSvgForScaling(svgContent)),
      catchError(error => {
        console.error('Failed to load SVG:', error);
        return of(null);
      })
    );
  }

  /**
   * Optimize SVG content for proper scaling within containers
   */
  optimizeSvgForScaling(svgString: string): string {
    if (!svgString) return svgString;

    const parser = new DOMParser();
    const doc = parser.parseFromString(svgString, 'image/svg+xml');
    const svg = doc.querySelector('svg');

    if (!svg) return svgString;

    // Ensure the SVG has proper scaling attributes
    svg.setAttribute('preserveAspectRatio', 'xMidYMid meet');

    // If no viewBox is set, create one from width/height
    if (!svg.getAttribute('viewBox')) {
      const width = svg.width?.baseVal?.value || 800;
      const height = svg.height?.baseVal?.value || 600;
      svg.setAttribute('viewBox', `0 0 ${width} ${height}`);
    }

    // Remove fixed width/height to allow responsive scaling
    svg.removeAttribute('width');
    svg.removeAttribute('height');

    // Set CSS for responsive scaling
    svg.style.width = '100%';
    svg.style.height = '100%';

    return new XMLSerializer().serializeToString(doc);
  }

  /**
   * Extract room IDs from SVG content
   */
  extractRoomIds(svgContent: string): string[] {
    if (!svgContent) return [];

    const parser = new DOMParser();
    const doc = parser.parseFromString(svgContent, 'image/svg+xml');
    const roomElements = doc.querySelectorAll('[id^="room-"]');
    
    return Array.from(roomElements).map(element => element.id);
  }

  /**
   * Highlight an element in the SVG
   */
  highlightElement(svgContent: string, elementId: string): string {
    if (!svgContent) return svgContent;

    const parser = new DOMParser();
    const doc = parser.parseFromString(svgContent, 'image/svg+xml');
    const element = doc.getElementById(elementId);

    if (element) {
      element.classList.add('highlighted');
      element.style.fill = 'rgba(var(--primary-color-rgb), 0.3)';
      element.style.stroke = 'var(--primary-color)';
      element.style.strokeWidth = '2';
    }

    return new XMLSerializer().serializeToString(doc);
  }

  /**
   * Add a CSS class to an element in the SVG
   */
  addClassToElement(svgContent: string, elementId: string, className: string): string {
    if (!svgContent) return svgContent;

    const parser = new DOMParser();
    const doc = parser.parseFromString(svgContent, 'image/svg+xml');
    const element = doc.getElementById(elementId);

    if (element) {
      element.classList.toggle(className);
    }

    return new XMLSerializer().serializeToString(doc);
  }

  /**
   * Remove highlighting from all elements
   */
  removeAllHighlights(svgContent: string): string {
    if (!svgContent) return svgContent;

    const parser = new DOMParser();
    const doc = parser.parseFromString(svgContent, 'image/svg+xml');
    const highlightedElements = doc.querySelectorAll('.highlighted');

    highlightedElements.forEach(element => {
      element.classList.remove('highlighted');
      element.removeAttribute('style');
    });

    return new XMLSerializer().serializeToString(doc);
  }

  /**
   * Get SVG dimensions from content
   */
  getSvgDimensions(svgContent: string): { width: number; height: number } {
    if (!svgContent) return { width: 0, height: 0 };

    const parser = new DOMParser();
    const doc = parser.parseFromString(svgContent, 'image/svg+xml');
    const svg = doc.querySelector('svg');

    if (!svg) return { width: 0, height: 0 };

    const viewBox = svg.getAttribute('viewBox');
    if (viewBox) {
      const [, , width, height] = viewBox.split(' ').map(Number);
      return { width, height };
    }

    const width = svg.width?.baseVal?.value || 800;
    const height = svg.height?.baseVal?.value || 600;
    return { width, height };
  }

  /**
   * Modify the fill color of an SVG element
   */
  setElementFill(svgString: string, elementId: string, color: string): string {
    if (!svgString || !elementId || !color) {
      return svgString;
    }

    const parser = new DOMParser();
    const doc = parser.parseFromString(svgString, 'image/svg+xml');
    const element = doc.getElementById(elementId);

    if (element) {
      element.setAttribute('fill', color);
      const serializer = new XMLSerializer();
      return serializer.serializeToString(doc);
    }

    return svgString;
  }
}
