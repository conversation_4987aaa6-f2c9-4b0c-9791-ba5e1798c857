import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams, HttpErrorResponse } from '@angular/common/http';
import { Observable, BehaviorSubject, of, timer } from 'rxjs';
import { catchError, map, switchMap, retry, delay } from 'rxjs/operators';

/**
 * Home Assistant entity interface representing a device or entity
 */
export interface HAEntity {
  entity_id: string;
  state: string;
  attributes: Record<string, any>;
  last_changed: string;
  last_updated: string;
  context: { id: string; parent_id?: string; user_id?: string };
}

/**
 * Home Assistant connection configuration
 */
export interface HAConfig {
  apiUrl: string;
  token: string;
}

/**
 * Service call target
 */
export interface HAServiceTarget {
  entity_id?: string | string[];
  device_id?: string | string[];
  area_id?: string | string[];
}

/**
 * Service call parameters for controlling Home Assistant entities
 */
export interface HAServiceCall {
  domain: string;
  service: string;
  target?: HAServiceTarget;
  service_data?: Record<string, any>;
}

/**
 * Result of a service call to Home Assistant
 */
export interface HAServiceCallResult {
  success: boolean;
  error?: string;
  response?: any;
}

/**
 * Simplified Home Assistant Service for Dashboard Tiles Library
 * This is a lightweight version focused on basic entity management
 */
@Injectable({
  providedIn: 'root'
})
export class HomeAssistantService {
  private apiUrl: string | null = null;
  private token: string | null = null;
  private headers: HttpHeaders | undefined;
  
  // Observable to track connection status
  private connectionStatus = new BehaviorSubject<boolean>(false);
  public connectionStatus$ = this.connectionStatus.asObservable();
  
  // Observable to track entity states
  private entityStates = new BehaviorSubject<HAEntity[]>([]);
  public entityStates$ = this.entityStates.asObservable();
  
  // Polling interval (in milliseconds)
  private pollingInterval = 10000;
  private pollingSubscription: any = null;

  // Mock data for development testing
  private mockEntities: HAEntity[] = [
    {
      entity_id: 'light.living_room',
      state: 'on',
      attributes: {
        friendly_name: 'Living Room Light',
        brightness: 255,
        rgb_color: [255, 255, 255],
        supported_features: 63
      },
      last_changed: new Date().toISOString(),
      last_updated: new Date().toISOString(),
      context: { id: 'mock-context-id-1' }
    },
    {
      entity_id: 'switch.kitchen',
      state: 'off',
      attributes: {
        friendly_name: 'Kitchen Switch',
        supported_features: 0
      },
      last_changed: new Date().toISOString(),
      last_updated: new Date().toISOString(),
      context: { id: 'mock-context-id-2' }
    },
    {
      entity_id: 'sensor.temperature',
      state: '22.5',
      attributes: {
        friendly_name: 'Temperature Sensor',
        unit_of_measurement: '°C',
        device_class: 'temperature'
      },
      last_changed: new Date().toISOString(),
      last_updated: new Date().toISOString(),
      context: { id: 'mock-context-id-3' }
    },
    {
      entity_id: 'binary_sensor.motion',
      state: 'off',
      attributes: {
        friendly_name: 'Motion Sensor',
        device_class: 'motion'
      },
      last_changed: new Date().toISOString(),
      last_updated: new Date().toISOString(),
      context: { id: 'mock-context-id-4' }
    }
  ];

  constructor(private http: HttpClient) {
    // Initialize with mock data for development
    this.initializeMockData();
  }
  
  /**
   * Initialize mock data for development
   */
  private initializeMockData(): void {
    // Simulate connection
    setTimeout(() => {
      this.connectionStatus.next(true);
      this.entityStates.next(this.mockEntities);
    }, 1000);
  }
  
  /**
   * Check if Home Assistant API is configured
   */
  public isConfigured(): boolean {
    // For the library version, we'll always return true to enable mock data
    return true;
  }
  
  /**
   * Configure the Home Assistant connection
   */
  public configure(config: HAConfig): void {
    this.apiUrl = config.apiUrl;
    this.token = config.token;
    
    this.headers = new HttpHeaders({
      'Authorization': `Bearer ${this.token}`,
      'Content-Type': 'application/json'
    });
    
    // Test connection
    this.testConnection();
  }
  
  /**
   * Test the connection to Home Assistant
   */
  private testConnection(): void {
    if (!this.apiUrl || !this.token) {
      this.connectionStatus.next(false);
      return;
    }
    
    // For now, just simulate a successful connection
    this.connectionStatus.next(true);
    this.startPolling();
  }
  
  /**
   * Start polling for entity updates
   */
  private startPolling(): void {
    if (this.pollingSubscription) {
      this.pollingSubscription.unsubscribe();
    }
    
    this.pollingSubscription = timer(0, this.pollingInterval).subscribe(() => {
      this.fetchEntityStates();
    });
  }
  
  /**
   * Fetch all entity states
   */
  private fetchEntityStates(): void {
    // For the library version, return mock data
    this.entityStates.next(this.mockEntities);
  }
  
  /**
   * Toggle an entity (turn on/off)
   */
  public toggleEntity(entityId: string): Observable<HAServiceCallResult> {
    const domain = entityId.split('.')[0];
    
    return this.callService({
      domain: domain,
      service: 'toggle',
      target: { entity_id: entityId }
    });
  }
  
  /**
   * Call a Home Assistant service
   */
  public callService(serviceCall: HAServiceCall): Observable<HAServiceCallResult> {
    // For the library version, simulate service calls
    console.log('Simulating service call:', serviceCall);
    
    // Update mock entity state
    if (serviceCall.target?.entity_id) {
      const entityId = Array.isArray(serviceCall.target.entity_id) 
        ? serviceCall.target.entity_id[0] 
        : serviceCall.target.entity_id;
      
      const entity = this.mockEntities.find(e => e.entity_id === entityId);
      if (entity) {
        // Toggle state for toggle service
        if (serviceCall.service === 'toggle') {
          entity.state = entity.state === 'on' ? 'off' : 'on';
        } else if (serviceCall.service === 'turn_on') {
          entity.state = 'on';
        } else if (serviceCall.service === 'turn_off') {
          entity.state = 'off';
        }
        
        entity.last_changed = new Date().toISOString();
        entity.last_updated = new Date().toISOString();
        
        // Emit updated entities
        this.entityStates.next([...this.mockEntities]);
      }
    }
    
    return of({
      success: true,
      response: { success: true }
    }).pipe(delay(300));
  }
  
  /**
   * Get entity by ID
   */
  public getEntity(entityId: string): Observable<HAEntity | null> {
    return this.entityStates$.pipe(
      map(entities => entities.find(e => e.entity_id === entityId) || null)
    );
  }
  
  /**
   * Get entities by domain
   */
  public getEntitiesByDomain(domain: string): Observable<HAEntity[]> {
    return this.entityStates$.pipe(
      map(entities => entities.filter(e => e.entity_id.startsWith(domain + '.')))
    );
  }
  
  /**
   * Disconnect and cleanup
   */
  public disconnect(): void {
    if (this.pollingSubscription) {
      this.pollingSubscription.unsubscribe();
      this.pollingSubscription = null;
    }
    
    this.connectionStatus.next(false);
    this.entityStates.next([]);
  }
}
