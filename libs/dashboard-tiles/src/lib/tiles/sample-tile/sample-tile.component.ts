import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { BaseTileComponent } from '../base-tile/base-tile.component';
import { DashboardItem } from '../../shared/models/dashboard.models';

@Component({
  selector: 'app-sample-tile',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatButtonModule,
    BaseTileComponent
  ],
  template: `
    <app-base-tile 
      [item]="item"
      [isEditingEnabled]="isEditingEnabled"
      [isLocked]="isLocked"
      (delete)="delete.emit($event)"
      (lockChange)="lockChange.emit($event)">
      
      <div class="sample-tile-content">
        <div class="sample-icon">
          <mat-icon>dashboard</mat-icon>
        </div>
        <h4>Sample Tile</h4>
        <p>This is a sample dashboard tile component.</p>
        <p>ID: {{ item.id }}</p>
        <p>Type: {{ item.type }}</p>
        
        <div class="sample-actions">
          <button mat-raised-button color="primary" (click)="onSampleAction()">
            Sample Action
          </button>
        </div>
      </div>
    </app-base-tile>
  `,
  styles: [`
    .sample-tile-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;
      height: 100%;
      padding: 16px;
    }
    
    .sample-icon {
      font-size: 48px;
      color: var(--primary-color, #3f51b5);
      margin-bottom: 16px;
    }
    
    .sample-icon mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
    }
    
    h4 {
      margin: 0 0 8px 0;
      color: var(--text-primary, #333);
    }
    
    p {
      margin: 4px 0;
      color: var(--text-secondary, #666);
      font-size: 14px;
    }
    
    .sample-actions {
      margin-top: 16px;
    }
  `]
})
export class SampleTileComponent {
  @Input() item!: DashboardItem;
  @Input() isEditingEnabled = false;
  @Input() isLocked = false;
  
  @Output() delete = new EventEmitter<DashboardItem>();
  @Output() lockChange = new EventEmitter<{item: DashboardItem, locked: boolean}>();
  
  onSampleAction(): void {
    console.log('Sample action clicked for tile:', this.item.id);
  }
}
