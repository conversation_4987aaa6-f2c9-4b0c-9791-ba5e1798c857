import type { <PERSON>a, StoryObj } from '@storybook/angular';
import { SampleTileComponent } from './sample-tile.component';
import { DashboardItem } from '../../shared/models/dashboard.models';

const meta: Meta<SampleTileComponent> = {
  title: 'Dashboard Tiles/Sample Tile',
  component: SampleTileComponent,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    isEditingEnabled: {
      control: 'boolean',
      description: 'Whether editing mode is enabled'
    },
    isLocked: {
      control: 'boolean',
      description: 'Whether the tile is locked'
    },
    item: {
      control: 'object',
      description: 'Dashboard item configuration'
    }
  },
};

export default meta;
type Story = StoryObj<SampleTileComponent>;

const sampleItem: DashboardItem = {
  id: 'sample-tile-1',
  type: 'sample',
  title: 'My Sample Tile',
  x: 0,
  y: 0,
  cols: 2,
  rows: 2
};

export const Default: Story = {
  args: {
    item: sampleItem,
    isEditingEnabled: false,
    isLocked: false
  },
  render: (args) => ({
    props: args,
    template: `
      <div style="width: 400px; height: 300px;">
        <app-sample-tile 
          [item]="item"
          [isEditingEnabled]="isEditingEnabled"
          [isLocked]="isLocked">
        </app-sample-tile>
      </div>
    `
  })
};

export const EditingMode: Story = {
  args: {
    item: sampleItem,
    isEditingEnabled: true,
    isLocked: false
  },
  render: (args) => ({
    props: args,
    template: `
      <div style="width: 400px; height: 300px;">
        <app-sample-tile 
          [item]="item"
          [isEditingEnabled]="isEditingEnabled"
          [isLocked]="isLocked">
        </app-sample-tile>
      </div>
    `
  })
};

export const Locked: Story = {
  args: {
    item: sampleItem,
    isEditingEnabled: true,
    isLocked: true
  },
  render: (args) => ({
    props: args,
    template: `
      <div style="width: 400px; height: 300px;">
        <app-sample-tile 
          [item]="item"
          [isEditingEnabled]="isEditingEnabled"
          [isLocked]="isLocked">
        </app-sample-tile>
      </div>
    `
  })
};

export const CustomTitle: Story = {
  args: {
    item: {
      ...sampleItem,
      title: 'Custom Sample Tile Title'
    },
    isEditingEnabled: false,
    isLocked: false
  },
  render: (args) => ({
    props: args,
    template: `
      <div style="width: 400px; height: 300px;">
        <app-sample-tile 
          [item]="item"
          [isEditingEnabled]="isEditingEnabled"
          [isLocked]="isLocked">
        </app-sample-tile>
      </div>
    `
  })
};
