import { Component, Input, OnInit, OnDestroy, AfterViewInit, ElementRef, ViewChild, OnChanges, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { catchError, of, Subscription, take } from 'rxjs';

// Import from the library
import { 
  FloorplanTile, 
  FloorplanMarker, 
  Room, 
  HADeviceMapping, 
  Point,
  DashboardItem
} from '../../shared/models/dashboard.models';
import { LoadingSpinnerComponent } from '../../shared/components/loading-spinner/loading-spinner.component';
import { EmptyStateComponent } from '../../shared/components/empty-state/empty-state.component';
import { RoomDetailComponent } from '../../shared/components/room-detail/room-detail.component';
import { SafeHtmlPipe } from '../../shared/pipes/safe-html.pipe';
import { SvgHelperService } from '../../shared/services/svg-helper.service';
import { NotificationService } from '../../shared/services/notification.service';
import { HomeAssistantService, HAEntity } from '../../shared/services/home-assistant.service';
import { BaseTileComponent } from '../base-tile/base-tile.component';

// Import sub-components
import { FloorplanViewerComponent } from './components/floorplan-viewer/floorplan-viewer.component';
import { MarkerOverlayComponent } from './components/marker-overlay/marker-overlay.component';
import { FloorplanPlaceholderComponent } from './components/floorplan-placeholder.component';

@Component({
  selector: 'app-floorplan-tile',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    BaseTileComponent,
    RoomDetailComponent,
    FloorplanViewerComponent,
    MarkerOverlayComponent
  ],
  template: `
    <app-base-tile 
      [item]="item"
      [isEditingEnabled]="isEditingEnabled"
      [isLocked]="isLocked"
      (delete)="delete.emit($event)"
      (lockChange)="lockChange.emit($event)">
      
      <!-- Custom tile actions in header -->
      <div tileActions *ngIf="isEditingEnabled">
        <button 
          class="tile-action-btn"
          [class.active]="isCreatingRoom"
          (click)="toggleRoomCreationMode()"
          [title]="isCreatingRoom ? 'Cancel room creation' : 'Create a custom room area'">
          <i class="fa" [class.fa-draw-polygon]="!isCreatingRoom" [class.fa-times]="isCreatingRoom"></i>
        </button>
        
        <button 
          class="tile-action-btn"
          (click)="configureFloorplan()"
          title="Configure floorplan">
          <i class="fa fa-cog"></i>
        </button>
      </div>
      
      <!-- Main floorplan content -->
      <div class="floorplan-content">
        <!-- Floorplan Viewer -->
        <div class="floorplan-viewer-wrapper">
          <app-floorplan-viewer
            [imagePath]="floorplanImage"
            [svgContent]="svgContent"
            [interactive]="tile.interactive || false"
            [isEditingEnabled]="isEditingEnabled"
            [isLoading]="isLoading"
            [hasError]="hasError"
            [errorMessage]="errorMessage"
            (svgContentChange)="onSvgContentChange($event)"
            (svgContainerClick)="onSvgContainerClick($event)"
            (roomClick)="onRoomClick($event.roomId, $event.event)"
            (svgLoaded)="onSvgLoaded()"
            (configureFloorplan)="configureFloorplan()"
            (retryLoad)="reloadImage()">
          </app-floorplan-viewer>

          <!-- Marker Overlay -->
          <app-marker-overlay
            *ngIf="tile.interactive && !isLoading"
            [markers]="allMarkers"
            [haEntities]="haEntities"
            [showLabels]="true"
            [isEditingEnabled]="isEditingEnabled"
            [loadingEntityIds]="loadingEntityIds"
            [errorEntityIds]="errorEntityIds"
            (markerClick)="onMarkerClick($event)"
            (deviceMarkerClick)="onDeviceMarkerClick($event)">
          </app-marker-overlay>

          <!-- Room Creation Mode Indicator -->
          <div *ngIf="isDrawingMode" class="room-creation-indicator">
            <i class="fa fa-draw-polygon"></i>
            <span *ngIf="currentRoomPoints.length === 0">Click to start drawing a room</span>
            <span *ngIf="currentRoomPoints.length > 0 && currentRoomPoints.length < 3">
              {{ currentRoomPoints.length }} point(s) added - need {{ 3 - currentRoomPoints.length }} more
            </span>
            <span *ngIf="currentRoomPoints.length >= 3">Click the first point to complete</span>
          </div>

          <!-- Home Assistant status indicator -->
          <div *ngIf="!haConnected && tile.haDevices && tile.haDevices.length > 0"
               class="ha-status-indicator offline">
            <i class="fas fa-exclamation-triangle"></i> Home Assistant Disconnected
          </div>
        </div>

        <!-- Rooms Display Section -->
        <div class="rooms-display" *ngIf="hasRooms">
          <div class="rooms-header">
            <h4>
              <i class="fa fa-home"></i>
              Rooms ({{ roomCount }})
            </h4>
            <button *ngIf="isEditingEnabled" class="toggle-rooms-btn" (click)="toggleRoomsExpanded()"
                    [title]="roomsExpanded ? 'Collapse rooms' : 'Expand rooms'">
              <i class="fa" [class.fa-chevron-up]="roomsExpanded" [class.fa-chevron-down]="!roomsExpanded"></i>
            </button>
          </div>

          <div class="rooms-grid" [class.expanded]="roomsExpanded" *ngIf="roomsExpanded || !isEditingEnabled">
            <div
              *ngFor="let room of roomsList; trackBy: trackByRoomId"
              class="room-card"
              [class.selected]="selectedRooms.has(room.id)"
              [class.highlighted]="highlightedAreaId === 'room-' + room.id"
              (click)="onRoomCardClick(room, $event)"
              [title]="(room?.description || room?.name || 'Room')">

              <!-- Room type icon -->
              <div class="room-icon" [style.background-color]="getRoomColor(room.type)">
                <i [class]="getRoomIcon(room.type)"></i>
              </div>

              <!-- Room info -->
              <div class="room-info">
                <div class="room-name">{{ room.name }}</div>
                <div class="room-type">{{ getRoomTypeLabel(room.type) }}</div>
                <div class="room-devices" *ngIf="room.devices && room.devices.length > 0">
                  <i class="fa fa-microchip"></i>
                  {{ room.devices.length }} device{{ room.devices.length !== 1 ? 's' : '' }}
                </div>
              </div>

              <!-- Room actions -->
              <div class="room-actions" *ngIf="isEditingEnabled">
                <button class="edit-room-btn" (click)="editRoomDetails(room.id); $event.stopPropagation()"
                        title="Edit room">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="delete-room-btn" (click)="deleteRoom(room.id); $event.stopPropagation()"
                        title="Delete room">
                  <i class="fa fa-trash"></i>
                </button>
              </div>
            </div>

            <!-- Empty state for rooms -->
            <div class="empty-rooms" *ngIf="roomCount === 0">
              <i class="fa fa-home"></i>
              <span>No rooms created yet</span>
              <button *ngIf="isEditingEnabled" class="create-first-room-btn" (click)="toggleRoomCreationMode()">
                <i class="fa fa-plus"></i> Create your first room
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Room Detail Modal -->
      <app-room-detail
        *ngIf="showRoomDetail && selectedRoom"
        [room]="selectedRoom"
        [isVisible]="showRoomDetail"
        (close)="closeRoomDetail()">
      </app-room-detail>
    </app-base-tile>
  `,
  styleUrls: ['./floorplan-tile.component.scss']
})
export class FloorplanTileComponent implements OnInit, OnDestroy, AfterViewInit, OnChanges {
  @Input() item!: FloorplanTile;
  @Input() isEditingEnabled = false;
  @Input() isLocked = false;
  
  @Output() delete = new EventEmitter<DashboardItem>();
  @Output() lockChange = new EventEmitter<{item: DashboardItem, locked: boolean}>();
  @Output() saveChanges = new EventEmitter<FloorplanTile>();
  
  // Get the tile as FloorplanTile for easier access
  get tile(): FloorplanTile {
    return this.item as FloorplanTile;
  }

  @ViewChild('svgContainer') svgContainer?: ElementRef;
  
  // Default image path
  floorplanImage = '/floorplan.svg';
  markers: FloorplanMarker[] = [];
  
  // SVG support
  isSvgFile = false;
  svgContent = '';
  svgDimensions = { width: 0, height: 0 };
  
  // Loading and error states
  isLoading = true;
  hasError = false;
  errorMessage = '';
  usePlaceholder = false;
  
  // Room/area highlighting functionality
  highlightedAreaId: string | null = null;
  
  // Room detail view
  selectedRoom: Room | undefined;
  showRoomDetail = false;

  // Inline room editing
  editingRoomId: string | null = null;
  editingRoomData: Room | null = null;
  
  // Home Assistant integration
  haConnected = false;
  haSubscriptions: Subscription = new Subscription();
  haEntities: HAEntity[] = [];
  generatedMarkers: FloorplanMarker[] = [];
  loadingEntityIds: string[] = [];
  errorEntityIds: string[] = [];
  
  // Timeout reference for cleanup
  private loadingTimeout: ReturnType<typeof setTimeout> | null = null;
  private refreshTimeout: ReturnType<typeof setTimeout> | null = null;
  
  // Room creation properties
  isCreatingRoom = false;
  currentRoomPoints: Point[] = [];
  currentRoomId = '';
  currentRoomName = '';
  currentRoomType: Room['type'] = 'room';
  currentRoomDescription = '';
  temporaryPolygonId = 'temp-polygon';
  polygonInProgressId = 'polygon-in-progress';
  finalPolygonId = 'custom-room-';
  showRoomDetailsForm = false;
  isDrawingMode = false;
  
  // Multi-room selection
  selectedRooms: Map<string, Room> = new Map();
  isMultiSelectMode = false;

  // Rooms display
  roomsExpanded = true;
  
  // Room colors based on type
  roomColors: Record<string, string> = {
    'room': 'rgba(var(--primary-color-rgb), 0.4)',
    'bathroom': 'rgba(77, 195, 236, 0.4)',
    'bedroom': 'rgba(139, 195, 74, 0.4)',
    'kitchen': 'rgba(255, 193, 7, 0.4)',
    'living': 'rgba(255, 152, 0, 0.4)',
    'hallway': 'rgba(189, 189, 189, 0.4)',
    'other': 'rgba(156, 39, 176, 0.4)'
  };
  
  constructor(
    private http: HttpClient,
    private svgHelper: SvgHelperService,
    private homeAssistantService: HomeAssistantService,
    private notificationService: NotificationService
  ) {}

  /**
   * Get all markers (manual + generated from HA)
   */
  get allMarkers(): FloorplanMarker[] {
    return [
      ...(this.markers || []),
      ...(this.generatedMarkers || [])
    ];
  }

  /**
   * Check if there are any rooms
   */
  get hasRooms(): boolean | undefined {
    return this.tile.rooms && Object.keys(this.tile.rooms).length > 0;
  }

  /**
   * Get room count
   */
  get roomCount(): number {
    return this.tile.rooms ? Object.keys(this.tile.rooms).length : 0;
  }

  /**
   * Get rooms as an array
   */
  get roomsList(): Room[] {
    if (!this.tile.rooms) return [];
    return Object.values(this.tile.rooms);
  }
  
  ngOnInit(): void {
    console.log('Floorplan tile initialized with item:', this.tile);
    
    // Validate input data
    this.validateInputData();
    
    // Initialize initial path from item
    if (this.tile.imagePath) {
      this.floorplanImage = this.tile.imagePath;
    }
    
    if (this.tile.markers && this.tile.markers.length > 0) {
      // Make a deep copy of markers to avoid mutation issues
      this.markers = JSON.parse(JSON.stringify(this.tile.markers));
    }
    
    // Check if it's an SVG file or data URL
    this.isSvgFile = this.floorplanImage.toLowerCase().endsWith('.svg') ||
                     this.floorplanImage.startsWith('data:image/svg+xml');
    
    // Subscribe to Home Assistant if configured
    this.initializeHomeAssistant();
    
    // Load the floorplan
    this.loadFloorplan();
  }

  ngAfterViewInit(): void {
    // Setup SVG interactivity if needed
    if (this.isSvgFile && this.svgContent) {
      setTimeout(() => this.setupSvgInteractivity(), 100);
    }
  }
  
  ngOnDestroy(): void {
    // Clean up timeouts
    if (this.loadingTimeout) {
      clearTimeout(this.loadingTimeout);
    }
    
    if (this.refreshTimeout) {
      clearTimeout(this.refreshTimeout);
    }
    
    // Clean up Home Assistant subscriptions
    this.haSubscriptions.unsubscribe();
  }
  
  ngOnChanges(): void {
    // Handle changes if needed
  }
  
  /**
   * Validate the input data
   */
  private validateInputData(): void {
    if (!this.tile) {
      console.error('Floorplan tile: No item provided');
      this.item = {
        id: 'placeholder',
        type: 'floorplan',
        imagePath: '/floorplan.svg',
        x: 0,
        y: 0,
        cols: 1,
        rows: 1
      } as FloorplanTile;
    }
    
    if (this.tile.type !== 'floorplan') {
      console.warn('Floorplan tile: Item type is not "floorplan"');
      this.tile.type = 'floorplan';
    }
  }
  
  /**
   * Load the floorplan image or SVG
   */
  private loadFloorplan(): void {
    this.isLoading = true;
    this.hasError = false;
    
    if (this.isSvgFile) {
      this.loadSvgContent();
    } else {
      this.checkImageExists();
    }
  }
  
  /**
   * Load SVG content
   */
  private loadSvgContent(): void {
    // Handle data URLs directly
    if (this.floorplanImage.startsWith('data:image/svg+xml')) {
      try {
        // Extract SVG content from data URL
        const base64Content = this.floorplanImage.split(',')[1];
        const svgContent = atob(base64Content);

        this.svgContent = svgContent;
        this.hasError = false;
        this.usePlaceholder = false;
        this.isLoading = false;

        // SVG content loaded successfully

        // Setup interactivity after the SVG is loaded
        setTimeout(() => this.setupSvgInteractivity(), 100);
      } catch (error) {
        this.hasError = true;
        this.errorMessage = 'Failed to decode SVG data URL';
        this.usePlaceholder = true;
        this.isLoading = false;
      }
      return;
    }

    // Load from URL
    this.svgHelper.loadSvgFromUrl(this.floorplanImage)
      .subscribe(svgContent => {
        if (svgContent) {
          this.svgContent = svgContent;
          this.hasError = false;
          this.usePlaceholder = false;
          this.isLoading = false;

          // Setup interactivity after the SVG is loaded
          setTimeout(() => this.setupSvgInteractivity(), 100);
        } else {
          this.hasError = true;
          this.errorMessage = 'Failed to load SVG floorplan';
          this.usePlaceholder = true;
          this.isLoading = false;
        }
      });
  }
  
  /**
   * Check if image exists
   */
  private checkImageExists(): void {
    const img = new Image();
    img.onload = () => {
      this.hasError = false;
      this.usePlaceholder = false;
      this.isLoading = false;
    };
    img.onerror = () => {
      this.hasError = true;
      this.errorMessage = 'Failed to load floorplan image';
      this.usePlaceholder = true;
      this.isLoading = false;
    };
    
    img.crossOrigin = 'anonymous';
    img.src = this.floorplanImage + `?t=${new Date().getTime()}`;
  }
  
  /**
   * Handle SVG content changes
   */
  onSvgContentChange(content: string): void {
    this.svgContent = content;
  }

  /**
   * Handle SVG container clicks
   */
  onSvgContainerClick(event: MouseEvent): void {
    if (this.isCreatingRoom) {
      // Handle room creation clicks
      this.handleRoomCreationClick(event);
    }
  }

  /**
   * Handle room clicks
   */
  onRoomClick(roomId: string, event?: MouseEvent): void {
    if (this.isCreatingRoom) return;

    const actualRoomId = roomId.replace('room-', '');

    // Check if we have detailed room information
    if (this.tile.rooms && this.tile.rooms[actualRoomId]) {
      this.selectedRoom = this.tile.rooms[actualRoomId];
      this.showRoomDetail = true;
    } else {
      // Create a placeholder room
      this.selectedRoom = {
        id: actualRoomId,
        name: this.getRoomName(actualRoomId),
        type: 'room'
      };
      this.showRoomDetail = true;
    }
  }

  /**
   * Handle SVG loaded event
   */
  onSvgLoaded(): void {
    this.setupSvgInteractivity();
  }

  /**
   * Handle marker clicks
   */
  onMarkerClick(marker: FloorplanMarker): void {
    console.log('Marker clicked:', marker);
    this.notificationService.info(`Clicked marker: ${marker.label || marker.type}`);
  }

  /**
   * Handle device marker clicks
   */
  onDeviceMarkerClick(marker: FloorplanMarker): void {
    if (!marker.entityId) return;

    // Find the entity
    const entity = this.haEntities.find(e => e.entity_id === marker.entityId);
    if (!entity) return;

    // Toggle the entity
    this.homeAssistantService.toggleEntity(marker.entityId)
      .pipe(take(1))
      .subscribe({
        next: () => {
          this.notificationService.success(`Toggled ${entity.attributes['friendly_name'] || marker.entityId}`);
        },
        error: (err) => {
          this.notificationService.error('Failed to toggle device');
          console.error('Error toggling entity:', err);
        }
      });
  }

  /**
   * Configure floorplan
   */
  configureFloorplan(): void {
    this.notificationService.info('Floorplan configuration not implemented in library version');
  }

  /**
   * Reload image
   */
  reloadImage(): void {
    this.loadFloorplan();
  }

  /**
   * Toggle room creation mode
   */
  toggleRoomCreationMode(): void {
    if (!this.isEditingEnabled) {
      this.notificationService.warning('Cannot edit rooms when dashboard is locked');
      return;
    }

    this.isCreatingRoom = !this.isCreatingRoom;
    this.isDrawingMode = this.isCreatingRoom;

    if (this.isCreatingRoom) {
      this.startRoomCreation();
    } else {
      this.cancelRoomCreation();
    }
  }

  /**
   * Toggle rooms expanded state
   */
  toggleRoomsExpanded(): void {
    this.roomsExpanded = !this.roomsExpanded;
  }

  /**
   * Handle room card clicks
   */
  onRoomCardClick(room: Room, event: MouseEvent): void {
    this.selectedRoom = room;
    this.showRoomDetail = true;
  }

  /**
   * Track by function for room list
   */
  trackByRoomId(index: number, room: Room): string {
    return room.id;
  }

  /**
   * Edit room details
   */
  editRoomDetails(roomId: string): void {
    this.notificationService.info('Room editing not implemented in library version');
  }

  /**
   * Delete room
   */
  deleteRoom(roomId: string): void {
    if (!this.tile.rooms || !this.tile.rooms[roomId]) {
      this.notificationService.error('Room not found');
      return;
    }

    const roomName = this.tile.rooms[roomId].name;
    delete this.tile.rooms[roomId];

    this.notificationService.success(`Room "${roomName}" deleted successfully`);
    this.saveChanges.emit(this.tile);
  }

  /**
   * Close room detail modal
   */
  closeRoomDetail(): void {
    this.showRoomDetail = false;
    this.selectedRoom = undefined;
  }

  /**
   * Get room color based on type
   */
  getRoomColor(type: Room['type']): string {
    return this.roomColors[type] || this.roomColors['room'];
  }

  /**
   * Get room icon based on type
   */
  getRoomIcon(type: Room['type']): string {
    const iconMap: Record<Room['type'], string> = {
      'room': 'fa fa-home',
      'bathroom': 'fa fa-bath',
      'bedroom': 'fa fa-bed',
      'kitchen': 'fa fa-utensils',
      'living': 'fa fa-couch',
      'hallway': 'fa fa-door-open',
      'other': 'fa fa-cube'
    };

    return iconMap[type] || 'fa fa-home';
  }

  /**
   * Get room type label
   */
  getRoomTypeLabel(type: Room['type']): string {
    const labelMap: Record<Room['type'], string> = {
      'room': 'Room',
      'bathroom': 'Bathroom',
      'bedroom': 'Bedroom',
      'kitchen': 'Kitchen',
      'living': 'Living Room',
      'hallway': 'Hallway',
      'other': 'Other'
    };

    return labelMap[type] || 'Room';
  }

  /**
   * Setup SVG interactivity
   */
  private setupSvgInteractivity(): void {
    // Basic setup - can be extended
    console.log('Setting up SVG interactivity');
  }

  /**
   * Initialize Home Assistant integration
   */
  private initializeHomeAssistant(): void {
    if (!this.homeAssistantService.isConfigured()) {
      return;
    }

    // Monitor connection status
    const connectionSub = this.homeAssistantService.connectionStatus$.subscribe(connected => {
      this.haConnected = connected;

      if (connected) {
        this.fetchHomeAssistantDevices();
      }
    });

    // Get all entity states
    const entitiesSub = this.homeAssistantService.entityStates$.subscribe(entities => {
      if (entities && entities.length > 0) {
        this.haEntities = entities;
        this.updateDeviceMarkers();
      }
    });

    this.haSubscriptions.add(connectionSub);
    this.haSubscriptions.add(entitiesSub);
  }

  /**
   * Fetch Home Assistant devices
   */
  private fetchHomeAssistantDevices(): void {
    // This will be handled by the service subscriptions
  }

  /**
   * Update device markers based on HA entities
   */
  private updateDeviceMarkers(): void {
    if (!this.tile.haDevices || !this.haEntities || this.haEntities.length === 0) {
      return;
    }

    this.generatedMarkers = [];

    this.tile.haDevices.forEach(deviceMapping => {
      const entity = this.haEntities.find(e => e.entity_id === deviceMapping.entityId);

      if (entity) {
        const marker = this.createMarkerFromEntity(entity, deviceMapping);
        if (marker) {
          this.generatedMarkers.push(marker);
        }
      }
    });
  }

  /**
   * Create a marker from a Home Assistant entity
   */
  private createMarkerFromEntity(entity: HAEntity, mapping: HADeviceMapping): FloorplanMarker | null {
    if (!entity || !mapping || !mapping.position) return null;

    const domain = entity.entity_id.split('.')[0];
    const markerType = mapping.markerType || this.getMarkerTypeFromDomain(domain);

    return {
      id: `ha-${entity.entity_id}`,
      x: mapping.position.x,
      y: mapping.position.y,
      type: markerType as any,
      label: mapping.showLabel ? entity.attributes['friendly_name'] || entity.entity_id : undefined,
      entityId: entity.entity_id,
      showState: mapping.showState || false,
      stateAttribute: mapping.stateAttribute,
      icon: mapping.icon || this.getIconForEntity(entity),
      color: mapping.color || this.getColorForEntityState(entity)
    };
  }

  /**
   * Get marker type based on entity domain
   */
  private getMarkerTypeFromDomain(domain: string): string {
    switch (domain) {
      case 'light': return 'light';
      case 'switch': return 'device';
      case 'binary_sensor': return 'sensor';
      case 'sensor': return 'sensor';
      case 'climate': return 'device';
      case 'lock': return 'door';
      case 'cover': return 'window';
      default: return 'custom';
    }
  }

  /**
   * Get appropriate icon for an entity
   */
  private getIconForEntity(entity: HAEntity): string {
    if (entity.attributes['icon']) {
      return `fas fa-${entity.attributes['icon'].replace('mdi:', '')}`;
    }

    const domain = entity.entity_id.split('.')[0];
    switch (domain) {
      case 'light': return 'fas fa-lightbulb';
      case 'switch': return 'fas fa-power-off';
      case 'binary_sensor': return 'fas fa-dot-circle';
      case 'sensor': return 'fas fa-thermometer-half';
      case 'climate': return 'fas fa-temperature-high';
      case 'lock': return entity.state === 'locked' ? 'fas fa-lock' : 'fas fa-lock-open';
      case 'cover': return 'fas fa-window-maximize';
      default: return 'fas fa-cube';
    }
  }

  /**
   * Get color based on entity state
   */
  private getColorForEntityState(entity: HAEntity): string {
    const domain = entity.entity_id.split('.')[0];
    const state = entity.state;

    switch (domain) {
      case 'light':
      case 'switch':
        return state === 'on' ? '#ffcc00' : '#666666';
      case 'binary_sensor':
        return state === 'on' ? '#ff0000' : '#666666';
      case 'lock':
        return state === 'locked' ? '#00cc66' : '#ff0000';
      case 'cover':
        return state === 'open' ? '#00ccff' : '#666666';
      default:
        return state === 'on' ? '#00cc66' : '#666666';
    }
  }

  /**
   * Generate a readable room name from an ID
   */
  private getRoomName(roomId: string): string {
    return roomId
      .replace(/[-_]/g, ' ')
      .replace(/\w\S*/g, (txt) => txt.charAt(0).toUpperCase() + txt.substring(1).toLowerCase());
  }

  /**
   * Start room creation mode
   */
  private startRoomCreation(): void {
    this.currentRoomPoints = [];
    this.currentRoomId = this.generateUniqueRoomId();
    this.currentRoomName = '';
    this.currentRoomType = 'room';
    this.currentRoomDescription = '';
    this.showRoomDetailsForm = false;

    this.notificationService.info('Click on the floorplan to start drawing a room');
  }

  /**
   * Cancel room creation mode
   */
  private cancelRoomCreation(): void {
    this.isCreatingRoom = false;
    this.isDrawingMode = false;
    this.showRoomDetailsForm = false;
    this.currentRoomPoints = [];
  }

  /**
   * Generate a unique room ID
   */
  private generateUniqueRoomId(): string {
    const existingIds = this.tile.rooms ? Object.keys(this.tile.rooms) : [];
    let counter = 1;
    let newId = `room-${counter}`;

    while (existingIds.includes(newId)) {
      counter++;
      newId = `room-${counter}`;
    }

    return newId;
  }

  /**
   * Handle room creation clicks
   */
  private handleRoomCreationClick(event: MouseEvent): void {
    // Simplified room creation - just show a notification
    this.notificationService.info('Room creation functionality simplified for library version');
  }
}
