import type { Meta, StoryObj } from '@storybook/angular';
import { FloorplanTileComponent } from './floorplan-tile.component';
import { FloorplanTile, Room, FloorplanMarker } from '../../shared/models/dashboard.models';

const meta: Meta<FloorplanTileComponent> = {
  title: 'Dashboard Tiles/Floorplan Tile',
  component: FloorplanTileComponent,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    isEditingEnabled: {
      control: 'boolean',
      description: 'Whether editing mode is enabled'
    },
    isLocked: {
      control: 'boolean',
      description: 'Whether the tile is locked'
    },
    item: {
      control: 'object',
      description: 'Floorplan tile configuration'
    }
  },
};

export default meta;
type Story = StoryObj<FloorplanTileComponent>;

// Sample rooms data
const sampleRooms: { [id: string]: Room } = {
  'living-room': {
    id: 'living-room',
    name: 'Living Room',
    type: 'living',
    description: 'Main living area with TV and seating',
    devices: [
      {
        roomId: 'living-room',
        entityId: 'light.living_room',
        position: { x: 30, y: 40 },
        markerType: 'light',
        showLabel: true,
        showState: true
      }
    ]
  },
  'kitchen': {
    id: 'kitchen',
    name: 'Kitchen',
    type: 'kitchen',
    description: 'Cooking and dining area',
    devices: [
      {
        roomId: 'kitchen',
        entityId: 'switch.kitchen',
        position: { x: 70, y: 30 },
        markerType: 'switch',
        showLabel: true,
        showState: true
      }
    ]
  },
  'bedroom': {
    id: 'bedroom',
    name: 'Master Bedroom',
    type: 'bedroom',
    description: 'Main bedroom with ensuite',
    devices: []
  },
  'bathroom': {
    id: 'bathroom',
    name: 'Bathroom',
    type: 'bathroom',
    description: 'Main bathroom',
    devices: []
  }
};

// Sample markers
const sampleMarkers: FloorplanMarker[] = [
  {
    id: 'temp-sensor',
    x: 50,
    y: 50,
    type: 'sensor',
    label: 'Temperature',
    entityId: 'sensor.temperature',
    showState: true,
    icon: 'fa fa-thermometer-half',
    color: '#2196F3'
  },
  {
    id: 'motion-sensor',
    x: 25,
    y: 75,
    type: 'sensor',
    label: 'Motion',
    entityId: 'binary_sensor.motion',
    showState: true,
    icon: 'fa fa-running',
    color: '#FF5722'
  }
];

const baseTile: FloorplanTile = {
  id: 'floorplan-1',
  type: 'floorplan',
  title: 'Home Floorplan',
  x: 0,
  y: 0,
  cols: 4,
  rows: 3,
  imagePath: '/assets/sample-floorplan.svg',
  interactive: true,
  markers: sampleMarkers,
  rooms: sampleRooms,
  showDeviceStates: true,
  refreshInterval: 10000
};

export const Default: Story = {
  args: {
    item: baseTile,
    isEditingEnabled: false,
    isLocked: false
  },
  render: (args) => ({
    props: args,
    template: `
      <div style="width: 800px; height: 600px; border: 1px solid #ddd; border-radius: 8px; overflow: hidden;">
        <app-floorplan-tile 
          [item]="item"
          [isEditingEnabled]="isEditingEnabled"
          [isLocked]="isLocked">
        </app-floorplan-tile>
      </div>
    `
  })
};

export const EditingMode: Story = {
  args: {
    item: baseTile,
    isEditingEnabled: true,
    isLocked: false
  },
  render: (args) => ({
    props: args,
    template: `
      <div style="width: 800px; height: 600px; border: 1px solid #ddd; border-radius: 8px; overflow: hidden;">
        <app-floorplan-tile 
          [item]="item"
          [isEditingEnabled]="isEditingEnabled"
          [isLocked]="isLocked">
        </app-floorplan-tile>
      </div>
    `
  })
};

export const WithoutImage: Story = {
  args: {
    item: {
      ...baseTile,
      imagePath: ''
    },
    isEditingEnabled: false,
    isLocked: false
  },
  render: (args) => ({
    props: args,
    template: `
      <div style="width: 800px; height: 600px; border: 1px solid #ddd; border-radius: 8px; overflow: hidden;">
        <app-floorplan-tile 
          [item]="item"
          [isEditingEnabled]="isEditingEnabled"
          [isLocked]="isLocked">
        </app-floorplan-tile>
      </div>
    `
  })
};

export const MinimalConfiguration: Story = {
  args: {
    item: {
      id: 'floorplan-minimal',
      type: 'floorplan',
      title: 'Simple Floorplan',
      x: 0,
      y: 0,
      cols: 2,
      rows: 2,
      imagePath: '/assets/simple-floorplan.png',
      interactive: false
    } as FloorplanTile,
    isEditingEnabled: false,
    isLocked: false
  },
  render: (args) => ({
    props: args,
    template: `
      <div style="width: 400px; height: 300px; border: 1px solid #ddd; border-radius: 8px; overflow: hidden;">
        <app-floorplan-tile 
          [item]="item"
          [isEditingEnabled]="isEditingEnabled"
          [isLocked]="isLocked">
        </app-floorplan-tile>
      </div>
    `
  })
};

export const WithManyRooms: Story = {
  args: {
    item: {
      ...baseTile,
      rooms: {
        ...sampleRooms,
        'bedroom-2': {
          id: 'bedroom-2',
          name: 'Guest Bedroom',
          type: 'bedroom',
          description: 'Guest room with single bed'
        },
        'office': {
          id: 'office',
          name: 'Home Office',
          type: 'other',
          description: 'Work from home space'
        },
        'garage': {
          id: 'garage',
          name: 'Garage',
          type: 'other',
          description: 'Two car garage with storage'
        },
        'hallway': {
          id: 'hallway',
          name: 'Main Hallway',
          type: 'hallway',
          description: 'Central corridor'
        }
      }
    },
    isEditingEnabled: true,
    isLocked: false
  },
  render: (args) => ({
    props: args,
    template: `
      <div style="width: 800px; height: 700px; border: 1px solid #ddd; border-radius: 8px; overflow: hidden;">
        <app-floorplan-tile 
          [item]="item"
          [isEditingEnabled]="isEditingEnabled"
          [isLocked]="isLocked">
        </app-floorplan-tile>
      </div>
    `
  })
};

export const Locked: Story = {
  args: {
    item: baseTile,
    isEditingEnabled: true,
    isLocked: true
  },
  render: (args) => ({
    props: args,
    template: `
      <div style="width: 800px; height: 600px; border: 1px solid #ddd; border-radius: 8px; overflow: hidden;">
        <app-floorplan-tile 
          [item]="item"
          [isEditingEnabled]="isEditingEnabled"
          [isLocked]="isLocked">
        </app-floorplan-tile>
      </div>
    `
  })
};
