import type { Meta, StoryObj } from '@storybook/angular';
import { FloorplanTileComponent } from './floorplan-tile.component';
import { FloorplanTile, Room, FloorplanMarker } from '../../shared/models/dashboard.models';

const meta: Meta<FloorplanTileComponent> = {
  title: 'Dashboard Tiles/Floorplan Tile',
  component: FloorplanTileComponent,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    isEditingEnabled: {
      control: 'boolean',
      description: 'Whether editing mode is enabled'
    },
    isLocked: {
      control: 'boolean',
      description: 'Whether the tile is locked'
    },
    item: {
      control: 'object',
      description: 'Floorplan tile configuration'
    }
  },
};

export default meta;
type Story = StoryObj<FloorplanTileComponent>;

// Sample rooms data
const sampleRooms: { [id: string]: Room } = {
  'living-room': {
    id: 'living-room',
    name: 'Living Room',
    type: 'living',
    description: 'Main living area with TV and seating',
    devices: [
      {
        roomId: 'living-room',
        entityId: 'light.living_room',
        position: { x: 30, y: 40 },
        markerType: 'light',
        showLabel: true,
        showState: true
      }
    ]
  },
  'kitchen': {
    id: 'kitchen',
    name: 'Kitchen',
    type: 'kitchen',
    description: 'Cooking and dining area',
    devices: [
      {
        roomId: 'kitchen',
        entityId: 'switch.kitchen',
        position: { x: 70, y: 30 },
        markerType: 'switch',
        showLabel: true,
        showState: true
      }
    ]
  },
  'bedroom': {
    id: 'bedroom',
    name: 'Master Bedroom',
    type: 'bedroom',
    description: 'Main bedroom with ensuite',
    devices: []
  },
  'bathroom': {
    id: 'bathroom',
    name: 'Bathroom',
    type: 'bathroom',
    description: 'Main bathroom',
    devices: []
  }
};

// Sample markers
const sampleMarkers: FloorplanMarker[] = [
  {
    id: 'temp-sensor',
    x: 50,
    y: 50,
    type: 'sensor',
    label: 'Temperature',
    entityId: 'sensor.temperature',
    showState: true,
    icon: 'fa fa-thermometer-half',
    color: '#2196F3'
  },
  {
    id: 'motion-sensor',
    x: 25,
    y: 75,
    type: 'sensor',
    label: 'Motion',
    entityId: 'binary_sensor.motion',
    showState: true,
    icon: 'fa fa-running',
    color: '#FF5722'
  }
];

// Sample SVG floorplan for Storybook
const sampleSvgFloorplan = `
<svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="400" height="300" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

  <!-- Living Room -->
  <rect id="room-living-room" x="20" y="20" width="180" height="120"
        fill="rgba(63, 81, 181, 0.1)" stroke="#3f51b5" stroke-width="1"/>
  <text x="110" y="85" text-anchor="middle" font-family="Arial" font-size="12" fill="#333">Living Room</text>

  <!-- Kitchen -->
  <rect id="room-kitchen" x="220" y="20" width="160" height="80"
        fill="rgba(255, 193, 7, 0.1)" stroke="#FFC107" stroke-width="1"/>
  <text x="300" y="65" text-anchor="middle" font-family="Arial" font-size="12" fill="#333">Kitchen</text>

  <!-- Bedroom -->
  <rect id="room-bedroom" x="220" y="120" width="160" height="100"
        fill="rgba(139, 195, 74, 0.1)" stroke="#8BC34A" stroke-width="1"/>
  <text x="300" y="175" text-anchor="middle" font-family="Arial" font-size="12" fill="#333">Bedroom</text>

  <!-- Bathroom -->
  <rect id="room-bathroom" x="20" y="160" width="80" height="80"
        fill="rgba(77, 195, 236, 0.1)" stroke="#4DCAEC" stroke-width="1"/>
  <text x="60" y="205" text-anchor="middle" font-family="Arial" font-size="10" fill="#333">Bathroom</text>

  <!-- Hallway -->
  <rect id="room-hallway" x="120" y="160" width="80" height="80"
        fill="rgba(189, 189, 189, 0.1)" stroke="#BDBDBD" stroke-width="1"/>
  <text x="160" y="205" text-anchor="middle" font-family="Arial" font-size="10" fill="#333">Hallway</text>

  <!-- Doors -->
  <line x1="200" y1="80" x2="220" y2="80" stroke="#795548" stroke-width="3"/>
  <line x1="160" y1="140" x2="160" y2="160" stroke="#795548" stroke-width="3"/>
  <line x1="200" y1="200" x2="220" y2="200" stroke="#795548" stroke-width="3"/>

  <!-- Windows -->
  <line x1="20" y1="50" x2="20" y2="80" stroke="#00BCD4" stroke-width="4"/>
  <line x1="350" y1="20" x2="380" y2="20" stroke="#00BCD4" stroke-width="4"/>
</svg>`;

const baseTile: FloorplanTile = {
  id: 'floorplan-1',
  type: 'floorplan',
  title: 'Home Floorplan',
  x: 0,
  y: 0,
  cols: 4,
  rows: 3,
  imagePath: 'data:image/svg+xml;base64,' + btoa(sampleSvgFloorplan),
  interactive: true,
  markers: sampleMarkers,
  rooms: sampleRooms,
  showDeviceStates: true,
  refreshInterval: 10000
};

export const Default: Story = {
  args: {
    item: baseTile,
    isEditingEnabled: false,
    isLocked: false
  },
  render: (args) => ({
    props: args,
    template: `
      <div style="width: 800px; height: 600px; border: 1px solid #ddd; border-radius: 8px; overflow: hidden;">
        <app-floorplan-tile 
          [item]="item"
          [isEditingEnabled]="isEditingEnabled"
          [isLocked]="isLocked">
        </app-floorplan-tile>
      </div>
    `
  })
};

export const EditingMode: Story = {
  args: {
    item: baseTile,
    isEditingEnabled: true,
    isLocked: false
  },
  render: (args) => ({
    props: args,
    template: `
      <div style="width: 800px; height: 600px; border: 1px solid #ddd; border-radius: 8px; overflow: hidden;">
        <app-floorplan-tile 
          [item]="item"
          [isEditingEnabled]="isEditingEnabled"
          [isLocked]="isLocked">
        </app-floorplan-tile>
      </div>
    `
  })
};

export const WithoutImage: Story = {
  args: {
    item: {
      ...baseTile,
      imagePath: ''
    },
    isEditingEnabled: false,
    isLocked: false
  },
  render: (args) => ({
    props: args,
    template: `
      <div style="width: 800px; height: 600px; border: 1px solid #ddd; border-radius: 8px; overflow: hidden;">
        <app-floorplan-tile 
          [item]="item"
          [isEditingEnabled]="isEditingEnabled"
          [isLocked]="isLocked">
        </app-floorplan-tile>
      </div>
    `
  })
};

export const MinimalConfiguration: Story = {
  args: {
    item: {
      id: 'floorplan-minimal',
      type: 'floorplan',
      title: 'Simple Floorplan',
      x: 0,
      y: 0,
      cols: 2,
      rows: 2,
      imagePath: '/assets/simple-floorplan.png',
      interactive: false
    } as FloorplanTile,
    isEditingEnabled: false,
    isLocked: false
  },
  render: (args) => ({
    props: args,
    template: `
      <div style="width: 400px; height: 300px; border: 1px solid #ddd; border-radius: 8px; overflow: hidden;">
        <app-floorplan-tile 
          [item]="item"
          [isEditingEnabled]="isEditingEnabled"
          [isLocked]="isLocked">
        </app-floorplan-tile>
      </div>
    `
  })
};

export const WithManyRooms: Story = {
  args: {
    item: {
      ...baseTile,
      rooms: {
        ...sampleRooms,
        'bedroom-2': {
          id: 'bedroom-2',
          name: 'Guest Bedroom',
          type: 'bedroom',
          description: 'Guest room with single bed'
        },
        'office': {
          id: 'office',
          name: 'Home Office',
          type: 'other',
          description: 'Work from home space'
        },
        'garage': {
          id: 'garage',
          name: 'Garage',
          type: 'other',
          description: 'Two car garage with storage'
        },
        'hallway': {
          id: 'hallway',
          name: 'Main Hallway',
          type: 'hallway',
          description: 'Central corridor'
        }
      }
    },
    isEditingEnabled: true,
    isLocked: false
  },
  render: (args) => ({
    props: args,
    template: `
      <div style="width: 800px; height: 700px; border: 1px solid #ddd; border-radius: 8px; overflow: hidden;">
        <app-floorplan-tile 
          [item]="item"
          [isEditingEnabled]="isEditingEnabled"
          [isLocked]="isLocked">
        </app-floorplan-tile>
      </div>
    `
  })
};

export const Locked: Story = {
  args: {
    item: baseTile,
    isEditingEnabled: true,
    isLocked: true
  },
  render: (args) => ({
    props: args,
    template: `
      <div style="width: 800px; height: 600px; border: 1px solid #ddd; border-radius: 8px; overflow: hidden;">
        <app-floorplan-tile 
          [item]="item"
          [isEditingEnabled]="isEditingEnabled"
          [isLocked]="isLocked">
        </app-floorplan-tile>
      </div>
    `
  })
};
