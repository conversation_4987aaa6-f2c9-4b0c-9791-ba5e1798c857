import { Component, Input, Output, EventEmitter, ViewChild, ElementRef, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SafeHtmlPipe } from '../../../../shared/pipes/safe-html.pipe';
import { LoadingSpinnerComponent } from '../../../../shared/components/loading-spinner/loading-spinner.component';
import { EmptyStateComponent } from '../../../../shared/components/empty-state/empty-state.component';

@Component({
  selector: 'app-floorplan-viewer',
  standalone: true,
  imports: [
    CommonModule,
    SafeHtmlPipe,
    LoadingSpinnerComponent,
    EmptyStateComponent
  ],
  template: `
    <div class="floorplan-viewer" [class.interactive]="interactive">
      <!-- Loading State -->
      <app-loading-spinner 
        *ngIf="isLoading" 
        [overlay]="true"
        [message]="'Loading floorplan...'">
      </app-loading-spinner>
      
      <!-- SVG Content -->
      <div
        *ngIf="svgContent && !isLoading"
        #svgContainer
        class="svg-container"
        [class.editing-enabled]="isEditingEnabled"
        [innerHTML]="svgContent | safeHtml"
        (click)="onContainerClick($event)">
      </div>

      <!-- Test SVG without pipe -->
      <div *ngIf="svgContent && !isLoading" class="test-svg" style="border: 2px solid red; margin: 10px; padding: 10px;">
        <h4>Test SVG (without pipe):</h4>
        <div [innerHTML]="svgContent"></div>
      </div>

      <!-- Debug Info -->
      <div *ngIf="!isLoading" class="debug-info" style="position: absolute; top: 10px; left: 10px; background: rgba(0,0,0,0.8); color: white; padding: 5px; font-size: 10px; z-index: 1000; max-width: 300px;">
        <div>SVG Content: {{ svgContent ? 'Present (' + svgContent.length + ' chars)' : 'None' }}</div>
        <div>Image Path: {{ imagePath || 'None' }}</div>
        <div>Loading: {{ isLoading }}</div>
        <div>Error: {{ hasError }}</div>
        <div *ngIf="svgContent" style="margin-top: 5px; font-size: 8px; max-height: 100px; overflow: auto;">
          Raw SVG: {{ svgContent.substring(0, 200) }}...
        </div>
      </div>

      
      <!-- Regular Image Fallback -->
      <div *ngIf="!svgContent && !isLoading && imagePath" class="image-container">
        <img 
          [src]="imagePath" 
          [alt]="'Floorplan'"
          class="floorplan-image"
          (load)="onImageLoad()"
          (error)="onImageError()">
      </div>
      
      <!-- Error/Empty State -->
      <app-empty-state 
        *ngIf="!svgContent && !imagePath && !isLoading"
        title="No Floorplan"
        description="No floorplan image has been configured for this tile."
        icon="fa fa-home"
        actionText="Configure Floorplan"
        (actionClick)="configureFloorplan.emit()">
      </app-empty-state>
      
      <!-- Error State -->
      <app-empty-state 
        *ngIf="hasError && !isLoading"
        title="Failed to Load Floorplan"
        [description]="errorMessage"
        icon="fa fa-exclamation-triangle"
        actionText="Retry"
        (actionClick)="retryLoad.emit()">
      </app-empty-state>
    </div>
  `,
  styles: [`
    .floorplan-viewer {
      position: relative;
      width: 100%;
      height: 100%;
      min-height: 300px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f8f9fa;
      border-radius: 8px;
      overflow: hidden;
    }
    
    .svg-container {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 2px solid blue;
      background-color: rgba(255, 255, 0, 0.1);
    }
    
    .svg-container.editing-enabled {
      cursor: crosshair;
    }
    
    .svg-container :global(svg) {
      max-width: 100%;
      max-height: 100%;
      width: auto;
      height: auto;
    }
    
    .image-container {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .floorplan-image {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
      border-radius: 4px;
    }
    
    /* Interactive room styles */
    .svg-container.interactive :global([id^="room-"]) {
      cursor: pointer;
      transition: all 0.2s ease;
    }
    
    .svg-container.interactive :global([id^="room-"]:hover) {
      opacity: 0.8;
      filter: brightness(1.1);
    }
    
    .svg-container :global([id^="room-"].highlighted) {
      fill: rgba(var(--primary-color-rgb, 63, 81, 181), 0.3) !important;
      stroke: var(--primary-color, #3f51b5) !important;
      stroke-width: 2 !important;
    }
    
    .svg-container :global([id^="room-"].selected) {
      fill: rgba(var(--success-color-rgb, 76, 175, 80), 0.3) !important;
      stroke: var(--success-color, #4CAF50) !important;
      stroke-width: 2 !important;
    }
    
    .svg-container :global([id^="room-"].room-active) {
      fill: rgba(var(--accent-color-rgb, 255, 193, 7), 0.3) !important;
      stroke: var(--accent-color, #FFC107) !important;
      stroke-width: 2 !important;
    }
    
    /* Custom room styles */
    .svg-container :global(.custom-room) {
      fill: rgba(var(--primary-color-rgb, 63, 81, 181), 0.1);
      stroke: var(--primary-color, #3f51b5);
      stroke-width: 1;
      cursor: pointer;
      transition: all 0.2s ease;
    }
    
    .svg-container :global(.custom-room:hover) {
      fill: rgba(var(--primary-color-rgb, 63, 81, 181), 0.2);
    }
    
    /* Temporary room creation styles */
    .svg-container :global(.temporary-room) {
      fill: rgba(var(--primary-color-rgb, 63, 81, 181), 0.2);
      stroke: var(--primary-color, #3f51b5);
      stroke-width: 2;
      stroke-dasharray: 5,5;
      pointer-events: none;
    }
    
    .svg-container :global(.preview-room) {
      fill: rgba(var(--primary-color-rgb, 63, 81, 181), 0.1);
      stroke: var(--primary-color, #3f51b5);
      stroke-width: 1;
      stroke-dasharray: 3,3;
      pointer-events: none;
    }
    
    .svg-container :global(.vertex-marker) {
      cursor: pointer;
      transition: all 0.2s ease;
    }
    
    .svg-container :global(.vertex-marker:hover) {
      r: 8;
      filter: brightness(1.2);
    }
  `]
})
export class FloorplanViewerComponent implements OnChanges {
  @Input() imagePath?: string;
  @Input() svgContent?: string;
  @Input() interactive = false;
  @Input() isEditingEnabled = false;
  @Input() isLoading = false;
  @Input() hasError = false;
  @Input() errorMessage = '';
  
  @Output() svgContentChange = new EventEmitter<string>();
  @Output() svgContainerClick = new EventEmitter<MouseEvent>();
  @Output() roomClick = new EventEmitter<{roomId: string, event: MouseEvent}>();
  @Output() svgLoaded = new EventEmitter<void>();
  @Output() configureFloorplan = new EventEmitter<void>();
  @Output() retryLoad = new EventEmitter<void>();
  
  @ViewChild('svgContainer') svgContainer?: ElementRef;
  
  ngOnChanges(changes: SimpleChanges): void {
    if (changes['svgContent'] && this.svgContent) {
      // SVG content updated
      // Setup interactivity after SVG content changes
      setTimeout(() => {
        this.setupSvgInteractivity();
        this.svgLoaded.emit();
      }, 100);
    }
  }
  
  /**
   * Handle container click events
   */
  onContainerClick(event: MouseEvent): void {
    this.svgContainerClick.emit(event);
    
    // Check if clicked element is a room
    const target = event.target as Element;
    if (target && target.id && target.id.startsWith('room-')) {
      const roomId = target.id.replace('room-', '');
      this.roomClick.emit({ roomId, event });
    }
  }
  
  /**
   * Handle image load success
   */
  onImageLoad(): void {
    console.log('Floorplan image loaded successfully');
  }
  
  /**
   * Handle image load error
   */
  onImageError(): void {
    console.error('Failed to load floorplan image');
  }
  
  /**
   * Setup SVG interactivity
   */
  private setupSvgInteractivity(): void {
    if (!this.svgContainer || !this.interactive) return;
    
    const svgElement = this.svgContainer.nativeElement.querySelector('svg');
    if (!svgElement) return;
    
    // Add interactive class
    svgElement.classList.add('interactive');
    
    // Setup room click handlers
    const roomElements = svgElement.querySelectorAll('[id^="room-"]');
    roomElements.forEach((element: Element) => {
      element.addEventListener('click', (event: Event) => {
        event.stopPropagation();
        const mouseEvent = event as MouseEvent;
        const roomId = element.id.replace('room-', '');
        this.roomClick.emit({ roomId, event: mouseEvent });
      });
      
      // Add hover effects
      element.addEventListener('mouseenter', () => {
        if (this.isEditingEnabled) {
          element.classList.add('highlighted');
        }
      });
      
      element.addEventListener('mouseleave', () => {
        element.classList.remove('highlighted');
      });
    });
  }
}
