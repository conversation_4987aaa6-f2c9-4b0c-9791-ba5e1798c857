import type { Meta, StoryObj } from '@storybook/angular';
import { BaseTileComponent } from './base-tile.component';
import { DashboardItem } from '../../shared/models/dashboard.models';

const meta: Meta<BaseTileComponent> = {
  title: 'Dashboard Tiles/Base Tile',
  component: BaseTileComponent,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    isEditingEnabled: {
      control: 'boolean',
      description: 'Whether editing mode is enabled'
    },
    isLocked: {
      control: 'boolean',
      description: 'Whether the tile is locked'
    },
    item: {
      control: 'object',
      description: 'Dashboard item configuration'
    }
  },
};

export default meta;
type Story = StoryObj<BaseTileComponent>;

const sampleItem: DashboardItem = {
  id: 'sample-1',
  type: 'default',
  title: 'Sample Tile',
  x: 0,
  y: 0,
  cols: 2,
  rows: 2
};

export const Default: Story = {
  args: {
    item: sampleItem,
    isEditingEnabled: false,
    isLocked: false
  },
  render: (args) => ({
    props: args,
    template: `
      <div style="width: 400px; height: 300px;">
        <app-base-tile 
          [item]="item"
          [isEditingEnabled]="isEditingEnabled"
          [isLocked]="isLocked">
          <div style="padding: 20px; text-align: center;">
            <h4>Base Tile Content</h4>
            <p>This is the content area of the base tile.</p>
            <p>Any content can be projected here.</p>
          </div>
        </app-base-tile>
      </div>
    `
  })
};

export const EditingEnabled: Story = {
  args: {
    item: sampleItem,
    isEditingEnabled: true,
    isLocked: false
  },
  render: (args) => ({
    props: args,
    template: `
      <div style="width: 400px; height: 300px;">
        <app-base-tile 
          [item]="item"
          [isEditingEnabled]="isEditingEnabled"
          [isLocked]="isLocked">
          <div style="padding: 20px; text-align: center;">
            <h4>Editing Mode</h4>
            <p>Lock and delete buttons are visible in the header.</p>
          </div>
        </app-base-tile>
      </div>
    `
  })
};

export const Locked: Story = {
  args: {
    item: sampleItem,
    isEditingEnabled: true,
    isLocked: true
  },
  render: (args) => ({
    props: args,
    template: `
      <div style="width: 400px; height: 300px;">
        <app-base-tile 
          [item]="item"
          [isEditingEnabled]="isEditingEnabled"
          [isLocked]="isLocked">
          <div style="padding: 20px; text-align: center;">
            <h4>Locked Tile</h4>
            <p>This tile is locked and has a green border.</p>
          </div>
        </app-base-tile>
      </div>
    `
  })
};
