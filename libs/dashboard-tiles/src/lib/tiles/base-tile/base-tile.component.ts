import { Component, Input, Output, EventEmitter, ContentChild, TemplateRef, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DashboardItem } from '../../shared/models/dashboard.models';

@Component({
  selector: 'app-base-tile',
  standalone: true,
  imports: [
    CommonModule
  ],
  template: `
    <div class="tile-container" [ngClass]="{'editing-enabled': isEditingEnabled, 'locked': isLocked}">
      <div class="tile-header">
        <div class="tile-title">
          <h3>{{ item.title || 'Untitled Tile' }}</h3>
        </div>
        
        <!-- Custom action section for content projection -->
        <div class="tile-actions">
          <ng-content select="[tileActions]"></ng-content>
          
          <button
            *ngIf="isEditingEnabled"
            class="tile-lock-button"
            [title]="isLocked ? 'Unlock Tile' : 'Lock Tile'"
            (click)="toggleLock()">
            <i class="fa" [class.fa-lock]="isLocked" [class.fa-lock-open]="!isLocked"></i>
          </button>

          <button
            *ngIf="isEditingEnabled"
            class="tile-delete-button"
            title="Delete Tile"
            (click)="deleteTile()">
            <i class="fa fa-times"></i>
          </button>
        </div>
      </div>
      
      <div class="tile-content">
        <ng-content></ng-content>
      </div>
    </div>
  `,
  styles: [`
    .tile-container {
      display: flex;
      flex-direction: column;
      height: 100%;
      width: 100%;
      border-radius: 8px;
      background-color: var(--background-secondary, #ffffff);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }
    
    .tile-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      background-color: var(--primary-color, #3f51b5);
      color: white;
      height: 48px;
      min-height: 48px;
    }
    
    .tile-title {
      display: flex;
      align-items: center;
      overflow: hidden;
    }
    
    .tile-title h3 {
      margin: 0;
      font-size: 16px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    
    .tile-actions {
      display: flex;
      align-items: center;
      margin-left: auto;
    }
    
    .tile-content {
      flex: 1;
      overflow: auto;
      padding: 16px;
    }
    
    .tile-delete-button,
    .tile-lock-button {
      background: none;
      border: none;
      color: white;
      font-size: 16px;
      height: 32px;
      width: 32px;
      border-radius: 4px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
      opacity: 0.8;
    }

    .tile-delete-button:hover,
    .tile-lock-button:hover {
      opacity: 1;
      background-color: rgba(255, 255, 255, 0.1);
    }
    
    /* Button styles */
    .tile-actions button {
      color: white;
      opacity: 0.8;
      transition: opacity 0.2s ease;
    }
    
    .tile-actions button:hover {
      opacity: 1;
    }
    
    /* Custom icons inside the tile header */
    ::ng-deep .tile-actions [tileActions] {
      display: flex;
      align-items: center;
    }
    
    /* Locked and editing styles */
    .tile-container.locked {
      border: 2px solid var(--success-color, #4CAF50);
    }
    
    .tile-container.editing-enabled:not(.locked) {
      cursor: move;
    }
  `],
  encapsulation: ViewEncapsulation.None
})
export class BaseTileComponent {
  @Input() item!: DashboardItem;
  @Input() isEditingEnabled = false;
  @Input() isLocked = false;
  
  @Output() delete = new EventEmitter<DashboardItem>();
  @Output() lockChange = new EventEmitter<{item: DashboardItem, locked: boolean}>();
  
  /**
   * Get CSS styles for the tile container based on tile configuration
   */
  getTileStyle(): Record<string, string> {
    return {
      width: '100%',
      height: '100%'
    };
  }
  
  /**
   * Delete the tile
   */
  deleteTile(): void {
    this.delete.emit(this.item);
  }
  
  /**
   * Toggle the lock state of the tile
   */
  toggleLock(): void {
    this.isLocked = !this.isLocked;
    this.lockChange.emit({ item: this.item, locked: this.isLocked });
  }
}
