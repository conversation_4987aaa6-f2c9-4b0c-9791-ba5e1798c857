import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'lib-dashboard-tiles',
  imports: [CommonModule],
  template: `
    <div class="dashboard-tiles-library">
      <h2>Dashboard Tiles Library</h2>
      <p>This is the main component for the dashboard tiles library.</p>
      <p>Import individual tile components as needed.</p>
    </div>
  `,
  styles: [`
    .dashboard-tiles-library {
      padding: 20px;
      text-align: center;
    }

    h2 {
      color: var(--primary-color, #3f51b5);
      margin-bottom: 16px;
    }

    p {
      color: var(--text-secondary, #666);
      margin: 8px 0;
    }
  `]
})
export class DashboardTilesComponent {}
