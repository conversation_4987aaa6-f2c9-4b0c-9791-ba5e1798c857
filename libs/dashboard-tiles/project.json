{"name": "dashboard-tiles", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/dashboard-tiles/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:package", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/dashboard-tiles/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/dashboard-tiles/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/dashboard-tiles/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/dashboard-tiles/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}, "storybook": {"executor": "@storybook/angular:start-storybook", "options": {"port": 4400, "configDir": "libs/dashboard-tiles/.storybook", "browserTarget": "dashboard-tiles:build-storybook", "compodoc": false}, "configurations": {"ci": {"quiet": true}}}, "build-storybook": {"executor": "@storybook/angular:build-storybook", "outputs": ["{options.outputDir}"], "options": {"outputDir": "dist/storybook/dashboard-tiles", "configDir": "libs/dashboard-tiles/.storybook", "browserTarget": "dashboard-tiles:build-storybook", "compodoc": false}, "configurations": {"ci": {"quiet": true}}}}}