import type { Preview } from '@storybook/angular';

const preview: Preview = {
  parameters: {
    actions: { argTypesRegex: '^on[A-Z].*' },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/,
      },
    },
    docs: {
      description: {
        component: 'Dashboard Tiles Library Components',
      },
    },
  },
  decorators: [
    (story) => {
      // Add global CSS variables for consistent theming
      const styles = `
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <style>
          :root {
            --primary-color: #3f51b5;
            --primary-color-dark: #303f9f;
            --primary-color-rgb: 63, 81, 181;
            --accent-color: #FFC107;
            --accent-color-rgb: 255, 193, 7;
            --success-color: #4CAF50;
            --success-color-rgb: 76, 175, 80;
            --warning-color: #FF9800;
            --error-color: #F44336;
            --text-primary: #333;
            --text-secondary: #666;
            --text-tertiary: #999;
          }

          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
          }

          * {
            box-sizing: border-box;
          }
        </style>
      `;

      return {
        template: `${styles}<div>${story()}</div>`,
      };
    },
  ],
};

export default preview;